document.addEventListener('DOMContentLoaded', function() {
    // CRM Top Navigation functionality
    const crmNav = document.querySelector('.crm-nav');
    
    if (crmNav) {
        // Handle active states based on current URL
        const currentPath = window.location.pathname;
        const currentSearch = window.location.search;
        const navLinks = crmNav.querySelectorAll('.crm-nav-item');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            
            // Check if this link matches the current page
            if (href === currentPath || 
                (href.includes('leads.index') && currentPath.includes('/leads')) ||
                (href.includes('contacts.index') && currentPath.includes('/contacts')) ||
                (href.includes('contact-groups.index') && currentPath.includes('/contact-groups')) ||
                (href.includes('form_builder.index') && (currentPath.includes('/form_builder') || currentPath.includes('/form_response'))) ||
                (href.includes('pipeline.index') && (currentPath.includes('/pipelines') || currentPath.includes('/pipeline')))) {
                
                // Remove active class from all links
                navLinks.forEach(l => l.classList.remove('active'));
                
                // Add active class to current link
                link.classList.add('active');
            }
            
            // Handle filter parameters (if any remain)
            if (currentSearch.includes('filter=follow-up') && href.includes('filter=follow-up')) {
                navLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');
            }
            
            if (currentSearch.includes('filter=meta') && href.includes('filter=meta')) {
                navLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');
            }
        });
        
        // Add smooth scrolling for mobile
        if (window.innerWidth <= 768) {
            const activeLink = crmNav.querySelector('.crm-nav-item.active');
            if (activeLink) {
                activeLink.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'center'
                });
            }
        }
        
        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768) {
                const activeLink = crmNav.querySelector('.crm-nav-item.active');
                if (activeLink) {
                    activeLink.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                        inline: 'center'
                    });
                }
            }
        });
    }
    
    // Add loading states to navigation links
    const crmNavLinks = document.querySelectorAll('.crm-nav .crm-nav-item');
    crmNavLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Don't prevent default for external links or special cases
            if (this.getAttribute('href').startsWith('#')) {
                e.preventDefault();
                return;
            }
            
            // Add loading state
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';
            
            // Remove loading state after navigation
            setTimeout(() => {
                this.style.opacity = '';
                this.style.pointerEvents = '';
            }, 1000);
        });
    });
});

// Handle browser back/forward buttons
window.addEventListener('popstate', function() {
    // Re-run the active state logic
    setTimeout(() => {
        const crmNav = document.querySelector('.crm-nav');
        if (crmNav) {
            const currentPath = window.location.pathname;
            const currentSearch = window.location.search;
            const navLinks = crmNav.querySelectorAll('.crm-nav-item');
            
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                
                if (href === currentPath || 
                    (href.includes('leads.index') && currentPath.includes('/leads')) ||
                    (href.includes('contacts.index') && currentPath.includes('/contacts')) ||
                    (href.includes('contact-groups.index') && currentPath.includes('/contact-groups')) ||
                    (href.includes('form_builder.index') && (currentPath.includes('/form_builder') || currentPath.includes('/form_response'))) ||
                    (href.includes('pipeline.index') && (currentPath.includes('/pipelines') || currentPath.includes('/pipeline')))) {
                    
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                }
                
                if (currentSearch.includes('filter=follow-up') && href.includes('filter=follow-up')) {
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                }
                
                if (currentSearch.includes('filter=meta') && href.includes('filter=meta')) {
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                }
            });
        }
    }, 100);
}); 