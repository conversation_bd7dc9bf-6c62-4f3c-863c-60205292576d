@extends('layouts.admin')

@section('page-title')
    {{ __('Finance - Sales Management') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('finance.dashboard') }}">{{ __('Finance') }}</a></li>
    <li class="breadcrumb-item">{{ __('Sales') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-sm-12">
            <!-- Include Finance Horizontal Menu -->
            @include('layouts.finance_horizontal_menu')
            
            <!-- Sales Management Content -->
            <div class="row">
                <!-- Subscription Section -->
                <div class="col-lg-6 col-md-12 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="ti ti-repeat me-2"></i>{{ __('Subscription Management') }}
                            </h5>
                            <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#subscriptionModal">
                                <i class="ti ti-plus"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-4">
                                <div class="theme-avtar bg-primary mx-auto mb-3" style="width: 80px; height: 80px;">
                                    <i class="ti ti-calendar-repeat" style="font-size: 2rem;"></i>
                                </div>
                                <h6 class="mb-2">{{ __('Recurring Subscriptions') }}</h6>
                                <p class="text-muted">{{ __('Manage recurring billing and subscription plans for your customers') }}</p>
                            </div>
                            
                            <!-- Subscription Stats -->
                            <div class="row mb-4">
                                <div class="col-4 text-center">
                                    <h4 class="text-primary mb-1">0</h4>
                                    <small class="text-muted">{{ __('Active') }}</small>
                                </div>
                                <div class="col-4 text-center">
                                    <h4 class="text-warning mb-1">0</h4>
                                    <small class="text-muted">{{ __('Pending') }}</small>
                                </div>
                                <div class="col-4 text-center">
                                    <h4 class="text-success mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                                    <small class="text-muted">{{ __('MRR') }}</small>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#subscriptionModal">
                                    <i class="ti ti-list me-2"></i>{{ __('View Subscriptions') }}
                                </button>
                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSubscriptionModal">
                                    <i class="ti ti-plus me-2"></i>{{ __('Create Subscription') }}
                                </button>
                            </div>

                            <!-- Recent Subscriptions -->
                            <div class="mt-4">
                                <h6 class="mb-3">{{ __('Recent Activity') }}</h6>
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item d-flex justify-content-between align-items-center px-0 py-2">
                                        <div>
                                            <small class="text-muted">{{ __('No subscriptions yet') }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Installment Section -->
                <div class="col-lg-6 col-md-12 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="ti ti-calendar-time me-2"></i>{{ __('Installment Plans') }}
                            </h5>
                            <button class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#installmentModal">
                                <i class="ti ti-plus"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-4">
                                <div class="theme-avtar bg-success mx-auto mb-3" style="width: 80px; height: 80px;">
                                    <i class="ti ti-calendar-dollar" style="font-size: 2rem;"></i>
                                </div>
                                <h6 class="mb-2">{{ __('Payment Installments') }}</h6>
                                <p class="text-muted">{{ __('Set up installment payment plans for large purchases') }}</p>
                            </div>
                            
                            <!-- Installment Stats -->
                            <div class="row mb-4">
                                <div class="col-4 text-center">
                                    <h4 class="text-success mb-1">0</h4>
                                    <small class="text-muted">{{ __('Active') }}</small>
                                </div>
                                <div class="col-4 text-center">
                                    <h4 class="text-warning mb-1">0</h4>
                                    <small class="text-muted">{{ __('Pending') }}</small>
                                </div>
                                <div class="col-4 text-center">
                                    <h4 class="text-info mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                                    <small class="text-muted">{{ __('Total') }}</small>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-success" data-bs-toggle="modal" data-bs-target="#installmentModal">
                                    <i class="ti ti-list me-2"></i>{{ __('View Installments') }}
                                </button>
                                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createInstallmentModal">
                                    <i class="ti ti-plus me-2"></i>{{ __('Create Installment') }}
                                </button>
                            </div>

                            <!-- Recent Installments -->
                            <div class="mt-4">
                                <h6 class="mb-3">{{ __('Recent Activity') }}</h6>
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item d-flex justify-content-between align-items-center px-0 py-2">
                                        <div>
                                            <small class="text-muted">{{ __('No installments yet') }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales Overview -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>{{ __('Sales Overview') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <div class="card border-0 bg-light">
                                        <div class="card-body text-center">
                                            <i class="ti ti-currency-dollar text-primary mb-2" style="font-size: 2rem;"></i>
                                            <h5 class="mb-1">{{ \Auth::user()->priceFormat(0) }}</h5>
                                            <small class="text-muted">{{ __('Total Sales') }}</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <div class="card border-0 bg-light">
                                        <div class="card-body text-center">
                                            <i class="ti ti-repeat text-info mb-2" style="font-size: 2rem;"></i>
                                            <h5 class="mb-1">{{ \Auth::user()->priceFormat(0) }}</h5>
                                            <small class="text-muted">{{ __('Recurring Revenue') }}</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <div class="card border-0 bg-light">
                                        <div class="card-body text-center">
                                            <i class="ti ti-calendar-time text-warning mb-2" style="font-size: 2rem;"></i>
                                            <h5 class="mb-1">{{ \Auth::user()->priceFormat(0) }}</h5>
                                            <small class="text-muted">{{ __('Installment Revenue') }}</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-6 mb-3">
                                    <div class="card border-0 bg-light">
                                        <div class="card-body text-center">
                                            <i class="ti ti-users text-success mb-2" style="font-size: 2rem;"></i>
                                            <h5 class="mb-1">0</h5>
                                            <small class="text-muted">{{ __('Active Customers') }}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>{{ __('Quick Actions') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('customer.index') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                        <i class="ti ti-users mb-2" style="font-size: 1.5rem;"></i>
                                        <span>{{ __('Customers') }}</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('invoice.index') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                        <i class="ti ti-file-invoice mb-2" style="font-size: 1.5rem;"></i>
                                        <span>{{ __('Invoices') }}</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('proposal.index') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                        <i class="ti ti-file-description mb-2" style="font-size: 1.5rem;"></i>
                                        <span>{{ __('Quotes') }}</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('productservice.index') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                        <i class="ti ti-package mb-2" style="font-size: 1.5rem;"></i>
                                        <span>{{ __('Products') }}</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('report.income.summary') }}" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                        <i class="ti ti-chart-line mb-2" style="font-size: 1.5rem;"></i>
                                        <span>{{ __('Reports') }}</span>
                                    </a>
                                </div>
                                <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                                    <a href="{{ route('settings') }}" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                        <i class="ti ti-settings mb-2" style="font-size: 1.5rem;"></i>
                                        <span>{{ __('Settings') }}</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Subscription Modal -->
    <div class="modal fade" id="subscriptionModal" tabindex="-1" aria-labelledby="subscriptionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="subscriptionModalLabel">{{ __('Subscription Management') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">{{ __('Subscription management feature will be available soon. This will allow you to create and manage recurring billing for your customers.') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Subscription Modal -->
    <div class="modal fade" id="createSubscriptionModal" tabindex="-1" aria-labelledby="createSubscriptionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createSubscriptionModalLabel">{{ __('Create Subscription') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">{{ __('Create subscription feature will be available soon.') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Installment Modal -->
    <div class="modal fade" id="installmentModal" tabindex="-1" aria-labelledby="installmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="installmentModalLabel">{{ __('Installment Management') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">{{ __('Installment management feature will be available soon. This will allow you to set up payment plans for large purchases.') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Installment Modal -->
    <div class="modal fade" id="createInstallmentModal" tabindex="-1" aria-labelledby="createInstallmentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createInstallmentModalLabel">{{ __('Create Installment Plan') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">{{ __('Create installment plan feature will be available soon.') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                </div>
            </div>
        </div>
    </div>
@endsection
