# CRM API Integration Guide for Automation Builder

This documentation provides a comprehensive guide for integrating CRM APIs into your automation builder platform. Each API endpoint is mapped to potential automation actions with detailed payload structures and data source information.

## Available CRM & Lead Actions

Your automation builder can now support all the requested lead management actions:

### ✅ **CRM & Lead Actions Available**
- **Create/Update/Delete Lead** - Complete lead record lifecycle management
- **Move Lead from One Stage to Another** - Stage progression automation
- **Assign Lead to User/Team** - Automatic lead assignment and distribution
- **Add/Remove Lead Tags** - Dynamic lead categorization and labeling
- **Send Lead to Specific Pipeline** - Route leads to appropriate workflows
- **Export Lead Data** - Extract lead information for external systems
- **Update Lead Custom Fields** - Modify specialized data attributes

### 📊 **Data Source APIs for Dropdowns**
- Get All Leads, Deals, Customers, Pipelines, Stages, Sources, Labels, Users
- Dynamic dropdown population with real-time data
- Cascading dropdowns (Pipeline → Stages dependency)

### 🔗 **Webhook Integration**
- Real-time event triggers for lead/deal creation, updates, deletions, and stage changes
- Automatic webhook dispatching for external system integration

## Table of Contents
1. [Authentication Setup](#authentication-setup)
2. [Data Source APIs (For Dropdowns)](#data-source-apis-for-dropdowns)
3. [Action APIs (For Automation Triggers)](#action-apis-for-automation-triggers)
4. [Webhook Events](#webhook-events)
5. [Common Automation Scenarios](#common-automation-scenarios)
6. [Advanced Integration Features](#advanced-integration-features)

## Authentication Setup

### Step 1: Initial Login
**Endpoint:** `POST /api/login`
**Purpose:** Get authentication token for SSO token generation

```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "auth_token_here",
    "user": 123
  }
}
```

### Step 2: Generate SSO Token
**Endpoint:** `POST /api/generate-sso-token`
**Headers:** `Authorization: Bearer {auth_token}`

**Response:**
```json
{
  "success": true,
  "token": "sso_token_here",
  "expires_in": 900
}
```

### Step 3: Use SSO Token for CRM APIs
**Headers:** `Authorization: Bearer {sso_token}`
**Note:** SSO tokens expire in 15 minutes and need to be regenerated.

## Data Source APIs (For Dropdowns)

These APIs provide data for dropdown selections in your automation builder interface.

### 1. Get All Leads
**Endpoint:** `GET /api/crm/leads`
**Use Case:** Populate lead selection dropdowns
**Automation Builder Usage:** "Select Lead" dropdown in actions like "Move Lead to Stage"

**Response Structure:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "subject": "Website Inquiry",
      "stage": {
        "id": 1,
        "name": "New Lead"
      },
      "pipeline": {
        "id": 1,
        "name": "Sales Pipeline"
      },
      "labels_list": ["Hot Lead", "Website"]
    }
  ]
}
```

### 2. Get All Deals
**Endpoint:** `GET /api/crm/deals`
**Use Case:** Populate deal selection dropdowns
**Automation Builder Usage:** "Select Deal" dropdown in deal-related actions

**Response Structure:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Enterprise Software Deal",
      "price": 50000,
      "stage": {
        "id": 2,
        "name": "Proposal"
      },
      "pipeline": {
        "id": 1,
        "name": "Sales Pipeline"
      }
    }
  ]
}
```

### 3. Get All Pipelines
**Endpoint:** `GET /api/crm/pipelines`
**Use Case:** Populate pipeline selection dropdowns
**Automation Builder Usage:** Filter stages by pipeline, create leads/deals in specific pipelines

**Response Structure:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Sales Pipeline"
    },
    {
      "id": 2,
      "name": "Marketing Pipeline"
    }
  ]
}
```

### 4. Get Stages by Pipeline
**Endpoint:** `GET /api/crm/pipelines/{pipelineId}/stages`
**Use Case:** Populate stage dropdowns based on selected pipeline
**Automation Builder Usage:** "Move to Stage" actions - stages dropdown updates when pipeline is selected

**Response Structure:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "New Lead",
      "pipeline_id": 1
    },
    {
      "id": 2,
      "name": "Qualified",
      "pipeline_id": 1
    }
  ]
}
```

### 5. Get All Customers
**Endpoint:** `GET /api/crm/customers`
**Use Case:** Populate customer selection dropdowns
**Automation Builder Usage:** Assign customers to deals, create bookings for customers

### 6. Get All Sources
**Endpoint:** `GET /api/crm/sources`
**Use Case:** Populate source selection dropdowns
**Automation Builder Usage:** Create leads/deals with specific sources

### 7. Get All Labels
**Endpoint:** `GET /api/crm/labels`
**Use Case:** Populate label selection dropdowns (multi-select)
**Automation Builder Usage:** Add/remove labels from leads/deals

### 8. Get Calendar Events
**Endpoint:** `GET /api/booking/calendar-events`
**Use Case:** Populate event selection for booking creation
**Automation Builder Usage:** Create bookings for specific events

### 9. Get Users
**Endpoint:** `GET /api/crm/users`
**Use Case:** Populate user selection dropdowns for assignment actions
**Automation Builder Usage:** "Assign to User" dropdowns in lead/deal assignment actions

**Response Structure:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "John Smith",
      "email": "<EMAIL>",
      "type": "employee"
    }
  ]
}
```

## Action APIs (For Automation Triggers)

These APIs perform actions that can be triggered by your automation builder.

## CRM & Lead Actions

### 1. Create Lead
**Automation Action:** "Create New Lead"
**Endpoint:** `POST /api/crm/leads`

**Automation Builder Form Fields:**
- **Subject** (text input)
- **Name** (text input)
- **Email** (text input)
- **Pipeline** (dropdown from `/api/crm/pipelines`)
- **Stage** (dropdown from `/api/crm/pipelines/{pipelineId}/stages`)
- **Source** (dropdown from `/api/crm/sources`)
- **Products** (text input)
- **Notes** (textarea)
- **Labels** (multi-select from `/api/crm/labels`)
- **Follow-up Date** (date picker)
- **Assigned Users** (multi-select from `/api/crm/users`)

**Payload:**
```json
{
  "subject": "{{subject}}",
  "name": "{{name}}",
  "email": "{{email}}",
  "pipeline_id": "{{selected_pipeline_id}}",
  "stage_id": "{{selected_stage_id}}",
  "sources": "{{selected_source}}",
  "products": "{{products}}",
  "notes": "{{notes}}",
  "labels": [{{selected_label_ids}}],
  "next_follow_up_date": "{{follow_up_date}}",
  "users": [{{assigned_user_ids}}]
}
```

### 2. Update Lead
**Automation Action:** "Update Lead Information"
**Endpoint:** `PUT /api/crm/leads/{id}`

**Form Fields:** Same as Create Lead
**Usage:** Update existing lead data based on triggers

### 3. Delete Lead
**Automation Action:** "Delete Lead"
**Endpoint:** `DELETE /api/crm/leads/{id}`

**Form Fields:**
- **Lead** (dropdown from `/api/crm/leads`)

### 4. Move Lead to Stage
**Automation Action:** "Move Lead to Different Stage"
**Endpoint:** `POST /api/crm/leads/{id}/move-stage`

**Automation Builder Form Fields:**
- **Lead** (dropdown from `/api/crm/leads`)
- **New Stage** (dropdown from `/api/crm/pipelines/{pipelineId}/stages`)

**Payload:**
```json
{
  "stage_id": "{{selected_stage_id}}"
}
```

### 5. Assign Lead to Users
**Automation Action:** "Assign Lead to User/Team"
**Endpoint:** `POST /api/crm/leads/{id}/assign-users`

**Automation Builder Form Fields:**
- **Lead** (dropdown from `/api/crm/leads`)
- **Users** (multi-select from `/api/crm/users`)

**Payload:**
```json
{
  "user_ids": [{{selected_user_ids}}]
}
```

### 6. Unassign Lead from Users
**Automation Action:** "Remove Lead Assignment"
**Endpoint:** `DELETE /api/crm/leads/{id}/unassign-users`

**Form Fields:**
- **Lead** (dropdown from `/api/crm/leads`)
- **Users** (multi-select from `/api/crm/users`)

**Payload:**
```json
{
  "user_ids": [{{selected_user_ids}}]
}
```

### 7. Move Lead to Pipeline
**Automation Action:** "Send Lead to Specific Pipeline"
**Endpoint:** `POST /api/crm/leads/{id}/move-pipeline`

**Form Fields:**
- **Lead** (dropdown from `/api/crm/leads`)
- **Pipeline** (dropdown from `/api/crm/pipelines`)
- **Stage** (dropdown from `/api/crm/pipelines/{pipelineId}/stages`)

**Payload:**
```json
{
  "pipeline_id": "{{selected_pipeline_id}}",
  "stage_id": "{{selected_stage_id}}"
}
```

### 8. Add Lead Tags/Labels
**Automation Action:** "Add Tags to Lead"
**Endpoint:** `POST /api/crm/leads/{id}/add-labels`

**Form Fields:**
- **Lead** (dropdown from `/api/crm/leads`)
- **Labels** (multi-select from `/api/crm/labels`)

**Payload:**
```json
{
  "label_ids": [{{selected_label_ids}}]
}
```

### 9. Remove Lead Tags/Labels
**Automation Action:** "Remove Tags from Lead"
**Endpoint:** `DELETE /api/crm/leads/{id}/remove-labels`

**Form Fields:**
- **Lead** (dropdown from `/api/crm/leads`)
- **Labels** (multi-select from `/api/crm/labels`)

**Payload:**
```json
{
  "label_ids": [{{selected_label_ids}}]
}
```

### 10. Export Lead Data
**Automation Action:** "Export Lead Information"
**Endpoint:** `GET /api/crm/leads/export` (all leads) or `GET /api/crm/leads/{id}/export` (single lead)

**Form Fields:**
- **Export Type** (radio: "All Leads" or "Single Lead")
- **Lead** (dropdown from `/api/crm/leads` - only if "Single Lead" selected)

**Usage:** Extract lead data for external systems or reporting

### 11. Update Lead Custom Fields
**Automation Action:** "Update Lead Custom Fields"
**Endpoint:** `POST /api/crm/leads/{id}/custom-fields`

**Form Fields:**
- **Lead** (dropdown from `/api/crm/leads`)
- **Custom Fields** (dynamic fields based on `/api/crm/leads/{id}/custom-fields`)

**Payload:**
```json
{
  "custom_fields": [
    {
      "field_id": 1,
      "value": "{{custom_field_value}}"
    }
  ]
}
```

### Deal Actions

#### 1. Create Deal
**Automation Action:** "Create New Deal"
**Endpoint:** `POST /api/crm/deals`

**Automation Builder Form Fields:**
- **Name** (text input)
- **Phone** (text input)
- **Price** (number input)
- **Pipeline** (dropdown from `/api/crm/pipelines`)
- **Stage** (dropdown from `/api/crm/pipelines/{pipelineId}/stages`)
- **Source** (dropdown from `/api/crm/sources`)
- **Products** (text input)
- **Notes** (textarea)
- **Labels** (multi-select from `/api/crm/labels`)
- **Status** (dropdown: Active, Inactive)
- **Clients** (multi-select from `/api/crm/customers`)

#### 2. Move Deal to Stage
**Automation Action:** "Move Deal to Different Stage"
**Endpoint:** `POST /api/crm/deals/{id}/move-stage`

**Form Fields:**
- **Deal** (dropdown from `/api/crm/deals`)
- **New Stage** (dropdown from stages API)

### Customer Actions

#### 1. Create Customer
**Automation Action:** "Create New Customer"
**Endpoint:** `POST /api/crm/customers`

**Form Fields:**
- **Name** (text input)
- **Contact** (text input)
- **Email** (email input)
- **Tax Number** (text input, optional)

### Booking Actions

#### 1. Create Booking
**Automation Action:** "Create New Booking"
**Endpoint:** `POST /api/booking/bookings`

**Form Fields:**
- **Name** (text input)
- **Email** (email input)
- **Phone** (text input, optional)
- **Event** (dropdown from `/api/booking/calendar-events`)
- **Date** (date picker)
- **Time** (time picker)

## Webhook Events

Your automation builder can listen for these webhook events to trigger automations:

### Lead Events
- `crm.lead_created` - Triggered when a new lead is created
- `crm.lead_updated` - Triggered when a lead is updated
- `crm.lead_deleted` - Triggered when a lead is deleted
- `crm.lead_stage_changed` - Triggered when a lead moves to a different stage

### Deal Events
- `crm.deal_created` - Triggered when a new deal is created
- `crm.deal_updated` - Triggered when a deal is updated
- `crm.deal_deleted` - Triggered when a deal is deleted
- `crm.deal_stage_changed` - Triggered when a deal moves to a different stage

**Webhook Payload Example:**
```json
{
  "event": "crm.lead_stage_changed",
  "data": {
    "lead_id": 123,
    "old_stage_id": 1,
    "new_stage_id": 2,
    "lead": {
      "id": 123,
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## Common Automation Scenarios

### 1. Lead Nurturing Workflow
**Trigger:** New lead created (`crm.lead_created`)
**Actions:**
1. **Assign Lead to User** - Automatically assign to available sales rep
2. **Add Lead Tags** - Tag as "New Lead" and source-specific tags
3. **Update Lead Custom Fields** - Set lead score, priority, etc.
4. **Send to Pipeline** - Move to appropriate nurturing pipeline
5. Send welcome email (external system)
6. Add to email marketing list (external system)

### 2. Lead Qualification Process
**Trigger:** Lead updated with qualification criteria
**Actions:**
1. **Move Lead to Stage** - Move qualified leads to "Hot Prospects" stage
2. **Add Lead Tags** - Add "Qualified" tag
3. **Assign Lead to User** - Assign to senior sales rep
4. **Update Lead Custom Fields** - Update qualification score
5. **Export Lead Data** - Send to external CRM or marketing system
6. Create deal from qualified lead (external system)

### 3. Lead Distribution by Source
**Trigger:** New lead created from specific source
**Actions:**
1. **Move Lead to Pipeline** - Route to source-specific pipeline
2. **Assign Lead to User** - Assign based on source (web leads to team A, referrals to team B)
3. **Add Lead Tags** - Add source-specific tags
4. **Move Lead to Stage** - Set appropriate initial stage based on source

### 4. Lead Scoring and Prioritization
**Trigger:** Lead custom fields updated (lead score changed)
**Actions:**
1. **Add Lead Tags** - Add priority tags (Hot, Warm, Cold)
2. **Move Lead to Stage** - Move high-score leads to priority stage
3. **Assign Lead to User** - Assign high-priority leads to top performers
4. **Export Lead Data** - Send to external analytics system

### 5. Lead Cleanup and Management
**Trigger:** Scheduled automation (daily/weekly)
**Actions:**
1. **Export Lead Data** - Backup lead information
2. **Remove Lead Tags** - Clean up outdated tags
3. **Move Lead to Stage** - Move stale leads to "Follow-up Required" stage
4. **Update Lead Custom Fields** - Update last activity date

### 6. Deal Stage Progression
**Trigger:** Deal moved to "Proposal" stage (`crm.deal_stage_changed`)
**Actions:**
1. Create proposal document (external system)
2. Schedule presentation meeting (external system)
3. Send proposal email to client (external system)
4. Set reminder for follow-up (external system)

### 7. Booking Follow-up
**Trigger:** New booking created
**Actions:**
1. **Create Lead** - Create lead record from booking information
2. **Assign Lead to User** - Assign to booking handler
3. **Add Lead Tags** - Tag as "Booking Lead"
4. **Move Lead to Pipeline** - Send to booking follow-up pipeline
5. Send confirmation email (external system)

### 8. Lead Re-engagement Campaign
**Trigger:** Lead inactive for X days
**Actions:**
1. **Add Lead Tags** - Tag as "Re-engagement"
2. **Move Lead to Stage** - Move to "Re-engagement" stage
3. **Update Lead Custom Fields** - Update last contact attempt
4. **Export Lead Data** - Send to email marketing system for re-engagement campaign

### 9. Territory-Based Lead Assignment
**Trigger:** New lead created with location data
**Actions:**
1. **Assign Lead to User** - Assign based on territory/location
2. **Move Lead to Pipeline** - Route to territory-specific pipeline
3. **Add Lead Tags** - Add location-based tags
4. **Update Lead Custom Fields** - Set territory information

### 10. Lead Conversion Tracking
**Trigger:** Lead moved to "Converted" stage
**Actions:**
1. **Add Lead Tags** - Tag as "Converted"
2. **Update Lead Custom Fields** - Set conversion date and details
3. **Export Lead Data** - Send conversion data to analytics system
4. **Remove Lead Tags** - Remove "Active" tags

## Error Handling

All APIs return consistent error responses:

```json
{
  "error": "Validation failed",
  "messages": {
    "field_name": ["Error message"]
  }
}
```

**Common HTTP Status Codes:**
- `401` - Unauthorized (invalid/expired SSO token)
- `404` - Resource not found
- `422` - Validation errors
- `500` - Server error

## Rate Limiting

- SSO tokens expire every 15 minutes
- Implement token refresh logic in your automation builder
- Cache dropdown data to reduce API calls
- Use batch operations when possible

## Best Practices

1. **Token Management:** Implement automatic SSO token refresh
2. **Data Caching:** Cache dropdown data for better performance
3. **Error Handling:** Implement retry logic for failed API calls
4. **Validation:** Validate form data before sending to APIs
5. **Logging:** Log all API interactions for debugging
6. **Testing:** Use the provided test scripts to verify integration

This documentation provides the foundation for building powerful CRM automations. Each API endpoint can be mapped to specific actions in your automation builder, with dropdown data populated from the corresponding data source APIs.

## Advanced Integration Features

### Dynamic Field Dependencies

Many CRM actions have dependent fields that should update based on user selections:

#### Pipeline → Stages Dependency
When user selects a pipeline, the stages dropdown should update:

```javascript
// Automation Builder Implementation Example
onPipelineChange(pipelineId) {
  fetch(`/api/crm/pipelines/${pipelineId}/stages`, {
    headers: { 'Authorization': `Bearer ${ssoToken}` }
  })
  .then(response => response.json())
  .then(data => {
    updateStagesDropdown(data.data);
  });
}
```

#### Lead → Pipeline/Stage Dependency
When user selects a lead for moving, show current pipeline and available stages:

```javascript
onLeadChange(leadId) {
  fetch(`/api/crm/leads/${leadId}`, {
    headers: { 'Authorization': `Bearer ${ssoToken}` }
  })
  .then(response => response.json())
  .then(data => {
    const lead = data.data;
    showCurrentStage(lead.stage.name);
    loadAvailableStages(lead.pipeline.id);
  });
}
```

### Conditional Actions

Some automation actions should only be available based on certain conditions:

#### Stage-Based Actions
```json
{
  "action": "move_lead_to_stage",
  "conditions": {
    "current_stage": "New Lead",
    "available_stages": ["Qualified", "Unqualified", "Follow-up"]
  }
}
```

#### Deal Value-Based Actions
```json
{
  "action": "create_high_value_deal_task",
  "conditions": {
    "deal_price": ">= 10000"
  }
}
```

### Batch Operations

For efficiency, implement batch operations for multiple records:

#### Bulk Lead Stage Movement
**Endpoint:** `POST /api/crm/leads/move-to-stage`
**Use Case:** Move multiple leads to the same stage

```json
{
  "lead_ids": [123, 456, 789],
  "stage_id": 2,
  "order": [123, 456, 789]
}
```

### Data Validation Rules

Implement client-side validation before API calls:

#### Lead Creation Validation
```javascript
const validateLeadData = (formData) => {
  const errors = {};

  if (!formData.name) errors.name = "Name is required";
  if (!formData.email) errors.email = "Email is required";
  if (!formData.pipeline_id) errors.pipeline_id = "Pipeline is required";
  if (!formData.stage_id) errors.stage_id = "Stage is required";

  // Email format validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (formData.email && !emailRegex.test(formData.email)) {
    errors.email = "Invalid email format";
  }

  return errors;
};
```

### Real-time Updates

Implement real-time updates for better user experience:

#### WebSocket Integration
```javascript
// Listen for CRM updates
websocket.on('crm.lead_updated', (data) => {
  updateLeadInDropdown(data.lead_id, data.lead);
});

websocket.on('crm.stage_changed', (data) => {
  refreshStageDropdowns();
});
```

### Custom Field Support

Handle dynamic custom fields in your automation builder:

#### Get Custom Fields
**Endpoint:** `GET /api/crm/custom-fields?module=lead`
**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Lead Score",
      "type": "number",
      "module": "lead",
      "required": false
    },
    {
      "id": 2,
      "name": "Industry",
      "type": "select",
      "module": "lead",
      "options": ["Technology", "Healthcare", "Finance"]
    }
  ]
}
```

#### Dynamic Form Generation
```javascript
const generateCustomFields = (customFields) => {
  return customFields.map(field => {
    switch(field.type) {
      case 'text':
        return createTextInput(field);
      case 'number':
        return createNumberInput(field);
      case 'select':
        return createSelectInput(field);
      case 'date':
        return createDateInput(field);
      default:
        return createTextInput(field);
    }
  });
};
```

### Activity Tracking

Track activities for leads and deals:

#### Get Lead Activities
**Endpoint:** `GET /api/crm/leads/{leadId}/activity-logs`
**Use Case:** Display lead history in automation builder

#### Get Lead Tasks
**Endpoint:** `GET /api/crm/leads/{leadId}/tasks`
**Use Case:** Show pending tasks for context

### Integration Testing

Test your automation builder integration:

#### Test Automation Flow
```javascript
const testLeadCreationFlow = async () => {
  // 1. Test authentication
  const authToken = await login('<EMAIL>', 'password');
  const ssoToken = await generateSSOToken(authToken);

  // 2. Test data loading
  const pipelines = await getPipelines(ssoToken);
  const stages = await getStages(ssoToken, pipelines[0].id);
  const sources = await getSources(ssoToken);

  // 3. Test lead creation
  const leadData = {
    name: 'Test Lead',
    email: '<EMAIL>',
    pipeline_id: pipelines[0].id,
    stage_id: stages[0].id,
    sources: sources[0].name
  };

  const newLead = await createLead(ssoToken, leadData);
  console.log('Lead created:', newLead);

  // 4. Test lead movement
  const moveResult = await moveLeadToStage(ssoToken, newLead.id, stages[1].id);
  console.log('Lead moved:', moveResult);
};
```

### Performance Optimization

#### Caching Strategy
```javascript
class CRMDataCache {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  async getPipelines(ssoToken) {
    const cacheKey = 'pipelines';
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    const data = await fetch('/api/crm/pipelines', {
      headers: { 'Authorization': `Bearer ${ssoToken}` }
    }).then(r => r.json());

    this.cache.set(cacheKey, {
      data: data.data,
      timestamp: Date.now()
    });

    return data.data;
  }
}
```

#### Debounced Search
```javascript
const debouncedLeadSearch = debounce(async (searchTerm, ssoToken) => {
  const leads = await fetch(`/api/crm/leads?search=${searchTerm}`, {
    headers: { 'Authorization': `Bearer ${ssoToken}` }
  }).then(r => r.json());

  updateLeadDropdown(leads.data);
}, 300);
```

### Error Recovery

Implement robust error handling:

#### Token Refresh Logic
```javascript
class APIClient {
  constructor() {
    this.authToken = null;
    this.ssoToken = null;
    this.ssoTokenExpiry = null;
  }

  async makeRequest(endpoint, options = {}) {
    try {
      // Check if SSO token needs refresh
      if (!this.ssoToken || Date.now() > this.ssoTokenExpiry) {
        await this.refreshSSOToken();
      }

      const response = await fetch(endpoint, {
        ...options,
        headers: {
          'Authorization': `Bearer ${this.ssoToken}`,
          'Content-Type': 'application/json',
          ...options.headers
        }
      });

      if (response.status === 401) {
        // Token expired, refresh and retry
        await this.refreshSSOToken();
        return this.makeRequest(endpoint, options);
      }

      return response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  async refreshSSOToken() {
    const response = await fetch('/api/generate-sso-token', {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${this.authToken}` }
    });

    const data = await response.json();
    this.ssoToken = data.token;
    this.ssoTokenExpiry = Date.now() + (data.expires_in * 1000);
  }
}
```

This comprehensive integration guide provides everything needed to build a robust automation builder that seamlessly integrates with the CRM system.
