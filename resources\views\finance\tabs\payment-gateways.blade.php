<!-- Payment Gateways Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-0">{{ __('Payment Gateway Settings') }}</h4>
        <p class="text-muted">{{ __('Configure payment methods for your business') }}</p>
    </div>
</div>

<!-- Payment Gateway Cards -->
<div class="row">
    <!-- Stripe -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="{{ asset('assets/images/payments/stripe.png') }}" alt="Stripe" class="img-fluid" style="max-height: 40px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="theme-avtar bg-primary mx-auto" style="width: 60px; height: 60px; display: none;">
                        <i class="ti ti-credit-card" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2">{{ __('Stripe') }}</h5>
                <p class="text-muted small mb-3">{{ __('Accept credit cards and digital payments') }}</p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="stripeToggle" {{ (env('STRIPE_KEY') && env('STRIPE_SECRET')) ? 'checked' : '' }}>
                    <label class="form-check-label ms-2" for="stripeToggle">
                        {{ __('Enable Stripe') }}
                    </label>
                </div>
                <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#stripeModal">
                    <i class="ti ti-settings me-1"></i>{{ __('Configure') }}
                </button>
            </div>
        </div>
    </div>

    <!-- PayPal -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="{{ asset('assets/images/payments/paypal.png') }}" alt="PayPal" class="img-fluid" style="max-height: 40px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="theme-avtar bg-info mx-auto" style="width: 60px; height: 60px; display: none;">
                        <i class="ti ti-brand-paypal" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2">{{ __('PayPal') }}</h5>
                <p class="text-muted small mb-3">{{ __('Accept PayPal payments worldwide') }}</p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="paypalToggle" {{ (env('PAYPAL_CLIENT_ID') && env('PAYPAL_CLIENT_SECRET')) ? 'checked' : '' }}>
                    <label class="form-check-label ms-2" for="paypalToggle">
                        {{ __('Enable PayPal') }}
                    </label>
                </div>
                <button class="btn btn-outline-info btn-sm" data-bs-toggle="modal" data-bs-target="#paypalModal">
                    <i class="ti ti-settings me-1"></i>{{ __('Configure') }}
                </button>
            </div>
        </div>
    </div>

    <!-- Razorpay -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="{{ asset('assets/images/payments/razorpay.png') }}" alt="Razorpay" class="img-fluid" style="max-height: 40px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="theme-avtar bg-success mx-auto" style="width: 60px; height: 60px; display: none;">
                        <i class="ti ti-currency-rupee" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2">{{ __('Razorpay') }}</h5>
                <p class="text-muted small mb-3">{{ __('Indian payment gateway solution') }}</p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="razorpayToggle" {{ (env('RAZORPAY_KEY') && env('RAZORPAY_SECRET')) ? 'checked' : '' }}>
                    <label class="form-check-label ms-2" for="razorpayToggle">
                        {{ __('Enable Razorpay') }}
                    </label>
                </div>
                <button class="btn btn-outline-success btn-sm" data-bs-toggle="modal" data-bs-target="#razorpayModal">
                    <i class="ti ti-settings me-1"></i>{{ __('Configure') }}
                </button>
            </div>
        </div>
    </div>

    <!-- Flutterwave -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="{{ asset('assets/images/payments/flutterwave.png') }}" alt="Flutterwave" class="img-fluid" style="max-height: 40px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="theme-avtar bg-warning mx-auto" style="width: 60px; height: 60px; display: none;">
                        <i class="ti ti-credit-card" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2">{{ __('Flutterwave') }}</h5>
                <p class="text-muted small mb-3">{{ __('African payment gateway') }}</p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="flutterwaveToggle" {{ (env('FLW_PUBLIC_KEY') && env('FLW_SECRET_KEY')) ? 'checked' : '' }}>
                    <label class="form-check-label ms-2" for="flutterwaveToggle">
                        {{ __('Enable Flutterwave') }}
                    </label>
                </div>
                <button class="btn btn-outline-warning btn-sm" data-bs-toggle="modal" data-bs-target="#flutterwaveModal">
                    <i class="ti ti-settings me-1"></i>{{ __('Configure') }}
                </button>
            </div>
        </div>
    </div>

    <!-- Paytm -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="{{ asset('assets/images/payments/paytm.png') }}" alt="Paytm" class="img-fluid" style="max-height: 40px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div class="theme-avtar bg-danger mx-auto" style="width: 60px; height: 60px; display: none;">
                        <i class="ti ti-wallet" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2">{{ __('Paytm') }}</h5>
                <p class="text-muted small mb-3">{{ __('Indian digital wallet and payments') }}</p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="paytmToggle" {{ (env('PAYTM_MERCHANT_ID') && env('PAYTM_MERCHANT_KEY')) ? 'checked' : '' }}>
                    <label class="form-check-label ms-2" for="paytmToggle">
                        {{ __('Enable Paytm') }}
                    </label>
                </div>
                <button class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#paytmModal">
                    <i class="ti ti-settings me-1"></i>{{ __('Configure') }}
                </button>
            </div>
        </div>
    </div>

    <!-- Bank Transfer -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="mb-3">
                    <div class="theme-avtar bg-secondary mx-auto" style="width: 60px; height: 60px;">
                        <i class="ti ti-building-bank" style="font-size: 1.5rem;"></i>
                    </div>
                </div>
                <h5 class="mb-2">{{ __('Bank Transfer') }}</h5>
                <p class="text-muted small mb-3">{{ __('Direct bank transfer payments') }}</p>
                <div class="form-check form-switch d-flex justify-content-center mb-3">
                    <input class="form-check-input" type="checkbox" id="bankToggle" checked>
                    <label class="form-check-label ms-2" for="bankToggle">
                        {{ __('Enable Bank Transfer') }}
                    </label>
                </div>
                <button class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal" data-bs-target="#bankModal">
                    <i class="ti ti-settings me-1"></i>{{ __('Configure') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Payment Settings -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Payment Settings') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="mb-3">
                            <label for="defaultCurrency" class="form-label">{{ __('Default Currency') }}</label>
                            <select class="form-select" id="defaultCurrency">
                                <option value="USD">{{ __('USD - US Dollar') }}</option>
                                <option value="EUR">{{ __('EUR - Euro') }}</option>
                                <option value="GBP">{{ __('GBP - British Pound') }}</option>
                                <option value="INR">{{ __('INR - Indian Rupee') }}</option>
                                <option value="CAD">{{ __('CAD - Canadian Dollar') }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="mb-3">
                            <label for="paymentTerms" class="form-label">{{ __('Default Payment Terms') }}</label>
                            <select class="form-select" id="paymentTerms">
                                <option value="net_15">{{ __('Net 15 days') }}</option>
                                <option value="net_30">{{ __('Net 30 days') }}</option>
                                <option value="net_45">{{ __('Net 45 days') }}</option>
                                <option value="net_60">{{ __('Net 60 days') }}</option>
                                <option value="due_on_receipt">{{ __('Due on receipt') }}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoReminders" checked>
                            <label class="form-check-label" for="autoReminders">
                                {{ __('Send automatic payment reminders') }}
                            </label>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="lateFees">
                            <label class="form-check-label" for="lateFees">
                                {{ __('Apply late fees for overdue payments') }}
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transaction History -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('Recent Transactions') }}</h5>
                <a href="{{ route('transaction.index') }}" class="btn btn-primary btn-sm">
                    <i class="ti ti-eye me-1"></i>{{ __('View All') }}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{{ __('Date') }}</th>
                                <th>{{ __('Transaction ID') }}</th>
                                <th>{{ __('Customer') }}</th>
                                <th>{{ __('Gateway') }}</th>
                                <th>{{ __('Amount') }}</th>
                                <th>{{ __('Status') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="ti ti-credit-card" style="font-size: 3rem;"></i>
                                        <h5 class="mt-3">{{ __('No Transactions Yet') }}</h5>
                                        <p>{{ __('Payment transactions will appear here') }}</p>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Modals -->
@include('finance.payment-gateways.modals')

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Payment gateway toggles
    const toggles = document.querySelectorAll('.form-check-input[type="checkbox"]');
    
    toggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const gatewayName = this.id.replace('Toggle', '');
            const isEnabled = this.checked;
            
            // Here you would typically make an AJAX call to update the setting
            console.log(`${gatewayName} ${isEnabled ? 'enabled' : 'disabled'}`);
            
            // Show notification
            const message = isEnabled ? 
                `${gatewayName} has been enabled` : 
                `${gatewayName} has been disabled`;
            
            // You can add a toast notification here
            // showToast(message);
        });
    });
});
</script>
