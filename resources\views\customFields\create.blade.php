{{-- resources/views/custom-fields/create.blade.php --}}
{{ Form::open(['url' => route('custom-field.store'), 'class' => 'needs-validation', 'novalidate']) }}
<div class="modal-body">
    <div class="row">
        {{-- Field Name --}}
        <div class="form-group col-md-12">
            {{ Form::label('name', __('Custom Field Name'), ['class' => 'form-label']) }}<x-required></x-required>
            {{ Form::text('name', null, ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('Enter Custom Field Name'), 'id' => 'field_name']) }}
        </div>

        {{-- Unique Key (Auto-generated) --}}
        <div class="form-group col-md-12">
    {{ Form::label('unique_key', __('Unique Key'), ['class' => 'form-label']) }} <x-required />
    {{ Form::text('unique_key', null, ['class' => 'form-control', 'readonly' => true, 'id' => 'unique_key', 'required' => true]) }}
</div>

{{-- Field Type --}}
<div class="form-group">
    {{ Form::label('type', __('Type'), ['class' => 'form-label']) }} <x-required />
    {{ Form::select('type', $types, null, ['class' => 'form-control select', 'id' => 'type']) }}
</div>

{{-- Dynamic Options Input --}}
<div class="form-group" id="options-container" style="display:none;">
    {{ Form::label('options[]', __('Options (for checkbox/radio/select/multiselect)'), ['class' => 'form-label']) }}
    <div id="options-list">
        <div class="d-flex mb-2">
            {{ Form::text('options[]', null, ['class' => 'form-control me-2', 'placeholder' => 'Option 1']) }}
            <button type="button" class="btn btn-sm btn-success add-option">+</button>
        </div>
    </div>
</div>


        
        {{-- Module --}}
        <div class="form-group col-md-12">
            {{ Form::label('module', __('Module'), ['class' => 'form-label']) }}<x-required></x-required>
            {{ Form::select('module', $modules, null, ['class' => 'form-control select', 'required' => 'required']) }}
        </div>

        {{-- Is Required --}}
        <div class="form-group col-md-12">
            {{ Form::label('is_required', __('Is Required'), ['class' => 'form-label']) }}
            {{ Form::select('is_required', [0 => __('No'), 1 => __('Yes')], 0, ['class' => 'form-control select']) }}
        </div>

        {{-- Status --}}
        <div class="form-group col-md-12">
            {{ Form::label('status', __('Status'), ['class' => 'form-label']) }}
            {{ Form::select('status', [1 => __('Active'), 0 => __('Inactive')], 1, ['class' => 'form-control select']) }}
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="{{ __('Cancel') }}" class="btn btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{ __('Create') }}" class="btn btn-primary" id="create-custom-field-btn">
</div>
{{ Form::close() }}

<script>
    $(document).on('input change', 'input[name="name"], select[name="module"]', function () {
        const name = $('input[name="name"]').val().toLowerCase().trim();
        const module = $('select[name="module"]').val().toLowerCase().trim();

        const slug = function(text) {
            return text
                .replace(/\s+/g, '_')
                .replace(/[^\w\-]+/g, '')
                .replace(/\_\_+/g, '_')
                .replace(/^_+/, '')
                .replace(/_+$/, '');
        };

        if (name && module) {
            const key = '{' + '{' + slug(module) + '.' + slug(name) + '}' + '}';
            $('input[name="unique_key"]').val(key);
        } else {
            $('input[name="unique_key"]').val('');
        }
    });
</script>


<script>
    document.addEventListener('DOMContentLoaded', function () {
        const typeSelect = document.getElementById('type');
        const optionsContainer = document.getElementById('options-container');
        const optionsList = document.getElementById('options-list');

        function toggleOptions() {
            const selected = typeSelect.value;
            if (selected === 'checkbox' || selected === 'radio' || selected === 'select' || selected === 'multiselect') {
                optionsContainer.style.display = 'block';
            } else {
                optionsContainer.style.display = 'none';
                // Remove any added options when not in use
                optionsList.innerHTML = '';
                addOptionInput(); // Add one empty field by default
            }
        }

        function addOptionInput() {
            const inputDiv = document.createElement('div');
            inputDiv.className = 'd-flex mb-2';
            inputDiv.innerHTML = `
                <input type="text" name="options[]" class="form-control me-2" placeholder="Option">
                <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
            `;
            optionsList.appendChild(inputDiv);
        }

        optionsList.addEventListener('click', function (e) {
            if (e.target.classList.contains('remove-option')) {
                e.target.closest('.d-flex').remove();
            }
        });

        // Add option button (dynamically bind)
        document.addEventListener('click', function (e) {
            if (e.target.classList.contains('add-option')) {
                addOptionInput();
            }
        });

        typeSelect.addEventListener('change', toggleOptions);
        toggleOptions(); // initialize on load
    });

    // Handle form submission via AJAX
    $('#createCustomFieldForm').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var submitBtn = $('#create-custom-field-btn');
        var originalText = submitBtn.val();
        
        submitBtn.val('Creating...').prop('disabled', true);
        
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.success) {
                    // Close modal and reload page
                    $('.modal').modal('hide');
                    location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    var errorMessage = '';
                    $.each(xhr.responseJSON.errors, function(key, value) {
                        errorMessage += value[0] + '\n';
                    });
                    alert('Validation errors:\n' + errorMessage);
                } else {
                    alert('Error creating custom field');
                }
            },
            complete: function() {
                submitBtn.val(originalText).prop('disabled', false);
            }
        });
    });
</script>
