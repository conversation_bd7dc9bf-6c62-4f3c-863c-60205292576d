<?php

namespace Tests\Feature;

use App\Models\ContactGroup;
use App\Models\Lead;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ContactGroupTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'type' => 'company',
            'created_by' => 1
        ]);
    }

    /** @test */
    public function user_can_view_contact_groups_index()
    {
        $this->actingAs($this->user);
        
        $response = $this->get(route('contact-groups.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('contact-groups.index');
    }

    /** @test */
    public function user_can_create_contact_group()
    {
        $this->actingAs($this->user);
        
        $groupData = [
            'name' => 'Test Group',
            'description' => 'Test Description'
        ];
        
        $response = $this->postJson(route('contact-groups.store'), $groupData);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $this->assertDatabaseHas('contact_groups', [
            'name' => 'Test Group',
            'description' => 'Test Description',
            'created_by' => $this->user->creatorId()
        ]);
    }

    /** @test */
    public function user_can_update_contact_group()
    {
        $this->actingAs($this->user);
        
        $group = ContactGroup::create([
            'name' => 'Original Name',
            'description' => 'Original Description',
            'created_by' => $this->user->creatorId()
        ]);
        
        $updateData = [
            'name' => 'Updated Name',
            'description' => 'Updated Description'
        ];
        
        $response = $this->putJson(route('contact-groups.update', $group->id), $updateData);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $this->assertDatabaseHas('contact_groups', [
            'id' => $group->id,
            'name' => 'Updated Name',
            'description' => 'Updated Description'
        ]);
    }

    /** @test */
    public function user_can_get_available_contacts()
    {
        $this->actingAs($this->user);
        
        // Create some leads without contact groups
        Lead::factory()->count(3)->create([
            'created_by' => $this->user->creatorId(),
            'contact_group_id' => null,
            'is_active' => 1,
            'is_deleted' => 0
        ]);
        
        $response = $this->getJson(route('contact-groups.available-contacts'));
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        $response->assertJsonStructure([
            'success',
            'contacts' => [
                '*' => ['id', 'name', 'email', 'phone', 'type']
            ]
        ]);
    }

    /** @test */
    public function user_can_attach_contacts_to_group()
    {
        $this->actingAs($this->user);
        
        $group = ContactGroup::create([
            'name' => 'Test Group',
            'created_by' => $this->user->creatorId()
        ]);
        
        $leads = Lead::factory()->count(2)->create([
            'created_by' => $this->user->creatorId(),
            'contact_group_id' => null,
            'is_active' => 1,
            'is_deleted' => 0
        ]);
        
        $response = $this->postJson(route('contact-groups.attach-contacts', $group->id), [
            'contact_ids' => $leads->pluck('id')->toArray()
        ]);
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        foreach ($leads as $lead) {
            $this->assertDatabaseHas('leads', [
                'id' => $lead->id,
                'contact_group_id' => $group->id
            ]);
        }
    }

    /** @test */
    public function user_can_delete_contact_group()
    {
        $this->actingAs($this->user);
        
        $group = ContactGroup::create([
            'name' => 'Test Group',
            'created_by' => $this->user->creatorId()
        ]);
        
        $response = $this->deleteJson(route('contact-groups.destroy', $group->id));
        
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        
        $this->assertDatabaseMissing('contact_groups', [
            'id' => $group->id
        ]);
    }
}
