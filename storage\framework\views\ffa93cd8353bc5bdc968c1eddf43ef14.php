

<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Contact Groups')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('contacts.index')); ?>"><?php echo e(__('Contacts')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Contact Groups')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        <button type="button" class="btn btn-sm btn-success me-2" onclick="createNewContactGroup()">
            <i class="fa fa-plus"></i> <?php echo e(__('Create New Contact Group')); ?>

        </button>
        <a href="<?php echo e(route('contacts.index')); ?>" class="btn btn-sm btn-primary">
            <i class="fa fa-arrow-left"></i> <?php echo e(__('Back to Contacts')); ?>

        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table" id="contact-groups-table">
                            <thead>
                                <tr>
                                    <th><?php echo e(__('Contact Group Name')); ?></th>
                                    <th><?php echo e(__('Total Contact Numbers')); ?></th>
                                    <th><?php echo e(__('Action')); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $contactGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm me-3">
                                                    <div class="avatar-title bg-primary rounded-circle">
                                                        <i class="fas fa-users text-white"></i>
                                                    </div>
                                                </div>
                                                <div>
                                                    <strong><?php echo e($group->name); ?></strong>
                                                    <?php if($group->description): ?>
                                                        <br><small class="text-muted"><?php echo e($group->description); ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-info"><?php echo e($group->leads_count); ?></span>
                                        </td>
                                        <td class="text-center">
                                            <div class="action-btn">
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="viewGroupMembers('<?php echo e($group->id); ?>', '<?php echo e($group->name); ?>')"><i class="fa fa-eye me-2"></i><?php echo e(__('View Members')); ?></a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="attachContactToGroup('<?php echo e($group->id); ?>', '<?php echo e($group->name); ?>')"><i class="fa fa-user-plus me-2"></i><?php echo e(__('Attach a Contact')); ?></a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="editContactGroup('<?php echo e($group->id); ?>', '<?php echo e($group->name); ?>', '<?php echo e($group->description); ?>')"><i class="fa fa-edit me-2"></i><?php echo e(__('Edit Contact Group Name')); ?></a></li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><a class="dropdown-item text-danger" href="#" onclick="deleteContactGroup('<?php echo e($group->id); ?>', '<?php echo e($group->name); ?>')"><i class="fa fa-trash me-2"></i><?php echo e(__('Delete')); ?></a></li>
                                                </ul>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="3" class="text-center">
                                            <div class="py-4">
                                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                                <h5 class="text-muted"><?php echo e(__('No Contact Groups Found')); ?></h5>
                                                <p class="text-muted"><?php echo e(__('Create contact groups from the contacts page by selecting contacts and clicking "Add to Contact Group"')); ?></p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- View Group Members Modal -->
    <div class="modal fade" id="viewGroupMembersModal" tabindex="-1" aria-labelledby="viewGroupMembersModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewGroupMembersModalLabel"><?php echo e(__('Group Members')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="group-members-content">
                        <!-- Members will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Close')); ?></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create New Contact Group Modal -->
    <div class="modal fade" id="createContactGroupModal" tabindex="-1" aria-labelledby="createContactGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createContactGroupModalLabel"><?php echo e(__('Create New Contact Group')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="createContactGroupForm">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="group_name" class="form-label"><?php echo e(__('Contact Group Name')); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="group_name" name="name" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                        <button type="submit" class="btn btn-primary"><?php echo e(__('Create Group')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Contact Group Modal -->
    <div class="modal fade" id="editContactGroupModal" tabindex="-1" aria-labelledby="editContactGroupModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editContactGroupModalLabel"><?php echo e(__('Edit Contact Group')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editContactGroupForm">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <input type="hidden" id="edit_group_id" name="group_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_group_name" class="form-label"><?php echo e(__('Contact Group Name')); ?> <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_group_name" name="name" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                        <button type="submit" class="btn btn-primary"><?php echo e(__('Update Group')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Attach Contact to Group Modal -->
    <div class="modal fade" id="attachContactModal" tabindex="-1" aria-labelledby="attachContactModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="attachContactModalLabel"><?php echo e(__('Attach Contact to Group')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="attachContactForm">
                    <?php echo csrf_field(); ?>
                    <input type="hidden" id="attach_group_id" name="group_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label"><?php echo e(__('Select Contacts to Attach')); ?></label>
                            <div id="available-contacts-list" class="border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                <div class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i> <?php echo e(__('Loading available contacts...')); ?>

                                </div>
                            </div>
                        </div>
                        <div id="selected-contacts-summary" class="mt-3" style="display: none;">
                            <h6><?php echo e(__('Selected Contacts:')); ?></h6>
                            <div id="selected-contacts-list" class="d-flex flex-wrap gap-2"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                        <button type="submit" class="btn btn-primary" id="attachContactsBtn" disabled><?php echo e(__('Attach Selected Contacts')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
    <script>
        function viewGroupMembers(groupId, groupName) {
            $('#viewGroupMembersModalLabel').text('Members of ' + groupName);
            $('#group-members-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading members...</div>');
            $('#viewGroupMembersModal').modal('show');
            
            // Load group members via AJAX
            $.ajax({
                url: '/contact-groups/' + groupId + '/members',
                method: 'GET',
                success: function(response) {
                    if (response.success && response.members) {
                        let html = '<div class="table-responsive"><table class="table"><thead><tr><th>Name</th><th>Type</th><th>Email</th><th>Phone</th><th>Action</th></tr></thead><tbody>';

                        if (response.members.length > 0) {
                            response.members.forEach(function(member) {
                                html += '<tr>';
                                html += '<td>' + member.name + '</td>';
                                html += '<td><span class="badge bg-' + (member.type === 'Lead' ? 'success' : 'info') + '">' + member.type + '</span></td>';
                                html += '<td>' + (member.email || '-') + '</td>';
                                html += '<td>' + (member.phone || '-') + '</td>';
                                html += '<td><button class="btn btn-sm btn-outline-danger" onclick="removeMemberFromGroup(' + groupId + ', ' + member.id + ', \'' + member.name + '\')"><i class="fa fa-trash"></i></button></td>';
                                html += '</tr>';
                            });
                        } else {
                            html += '<tr><td colspan="5" class="text-center">No members found</td></tr>';
                        }

                        html += '</tbody></table></div>';
                        $('#group-members-content').html(html);
                    } else {
                        $('#group-members-content').html('<div class="alert alert-warning">Failed to load group members</div>');
                    }
                },
                error: function() {
                    $('#group-members-content').html('<div class="alert alert-danger">Error loading group members</div>');
                }
            });
        }

        function createNewContactGroup() {
            $('#createContactGroupForm')[0].reset();
            $('#createContactGroupModal').modal('show');
        }

        function editContactGroup(groupId, groupName, groupDescription) {
            $('#edit_group_id').val(groupId);
            $('#edit_group_name').val(groupName);
            $('#edit_group_description').val(groupDescription || '');
            $('#editContactGroupModal').modal('show');
        }

        function attachContactToGroup(groupId, groupName) {
            $('#attach_group_id').val(groupId);
            $('#attachContactModalLabel').text('Attach Contact to ' + groupName);
            $('#attachContactModal').modal('show');

            // Load available contacts
            loadAvailableContacts();
        }

        function loadAvailableContacts() {
            $('#available-contacts-list').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading available contacts...</div>');

            $.ajax({
                url: '/contact-groups/available-contacts',
                method: 'GET',
                success: function(response) {
                    if (response.success && response.contacts) {
                        let html = '';

                        if (response.contacts.length > 0) {
                            response.contacts.forEach(function(contact) {
                                html += '<div class="form-check mb-2">';
                                html += '<input class="form-check-input contact-checkbox" type="checkbox" value="' + contact.id + '" id="contact_' + contact.id + '">';
                                html += '<label class="form-check-label w-100" for="contact_' + contact.id + '">';
                                html += '<div class="d-flex align-items-center">';
                                html += '<div class="avatar-sm me-3">';
                                html += '<div class="avatar-title bg-info rounded-circle">';
                                html += '<i class="fas fa-user text-white"></i>';
                                html += '</div>';
                                html += '</div>';
                                html += '<div>';
                                html += '<strong>' + contact.name + '</strong>';
                                html += '<br><small class="text-muted">';
                                if (contact.email) html += contact.email + ' | ';
                                if (contact.phone) html += contact.phone;
                                html += '</small>';
                                html += '</div>';
                                html += '</div>';
                                html += '</label>';
                                html += '</div>';
                            });
                        } else {
                            html = '<div class="text-center text-muted py-4">';
                            html += '<i class="fas fa-users fa-2x mb-3"></i>';
                            html += '<p>No available contacts found. All contacts are already assigned to groups.</p>';
                            html += '</div>';
                        }

                        $('#available-contacts-list').html(html);

                        // Add event listeners for checkboxes
                        $('.contact-checkbox').on('change', updateSelectedContactsSummary);
                    } else {
                        $('#available-contacts-list').html('<div class="alert alert-warning">Failed to load available contacts</div>');
                    }
                },
                error: function() {
                    $('#available-contacts-list').html('<div class="alert alert-danger">Error loading available contacts</div>');
                }
            });
        }

        function updateSelectedContactsSummary() {
            const selectedContacts = $('.contact-checkbox:checked');
            const selectedCount = selectedContacts.length;

            if (selectedCount > 0) {
                $('#selected-contacts-summary').show();
                $('#attachContactsBtn').prop('disabled', false);

                let summaryHtml = '';
                selectedContacts.each(function() {
                    const contactId = $(this).val();
                    const contactLabel = $('label[for="contact_' + contactId + '"] strong').text();
                    summaryHtml += '<span class="badge bg-primary me-1">' + contactLabel + '</span>';
                });

                $('#selected-contacts-list').html(summaryHtml);
            } else {
                $('#selected-contacts-summary').hide();
                $('#attachContactsBtn').prop('disabled', true);
            }
        }

        function removeMemberFromGroup(groupId, leadId, leadName) {
            if (confirm('Are you sure you want to remove "' + leadName + '" from this contact group?')) {
                $.ajax({
                    url: '/contact-groups/' + groupId + '/leads/' + leadId,
                    method: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            show_toastr('success', response.message);
                            // Refresh the members list
                            viewGroupMembers(groupId, $('#viewGroupMembersModalLabel').text().replace('Members of ', ''));
                        } else {
                            show_toastr('error', response.message);
                        }
                    },
                    error: function() {
                        show_toastr('error', 'Error removing member from group');
                    }
                });
            }
        }

        function deleteContactGroup(groupId, groupName) {
            if (confirm('Are you sure you want to delete the contact group "' + groupName + '"? This action cannot be undone.')) {
                $.ajax({
                    url: '/contact-groups/' + groupId,
                    method: 'DELETE',
                    data: {
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            show_toastr('success', response.message);
                            location.reload();
                        } else {
                            show_toastr('error', response.message);
                        }
                    },
                    error: function() {
                        show_toastr('error', 'Error deleting contact group');
                    }
                });
            }
        }

        // Form submission handlers
        $('#createContactGroupForm').on('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            $.ajax({
                url: '/contact-groups',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        show_toastr('success', response.message);
                        $('#createContactGroupModal').modal('hide');
                        location.reload();
                    } else {
                        show_toastr('error', response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    if (response && response.message) {
                        show_toastr('error', response.message);
                    } else {
                        show_toastr('error', 'Error creating contact group');
                    }
                }
            });
        });

        $('#editContactGroupForm').on('submit', function(e) {
            e.preventDefault();

            const groupId = $('#edit_group_id').val();
            const formData = new FormData(this);

            $.ajax({
                url: '/contact-groups/' + groupId,
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        show_toastr('success', response.message);
                        $('#editContactGroupModal').modal('hide');
                        location.reload();
                    } else {
                        show_toastr('error', response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    if (response && response.message) {
                        show_toastr('error', response.message);
                    } else {
                        show_toastr('error', 'Error updating contact group');
                    }
                }
            });
        });

        $('#attachContactForm').on('submit', function(e) {
            e.preventDefault();

            const groupId = $('#attach_group_id').val();
            const selectedContactIds = $('.contact-checkbox:checked').map(function() {
                return parseInt($(this).val());
            }).get();

            if (selectedContactIds.length === 0) {
                show_toastr('error', 'Please select at least one contact to attach');
                return;
            }

            $.ajax({
                url: '/contact-groups/' + groupId + '/attach-contacts',
                method: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    contact_ids: selectedContactIds
                },
                success: function(response) {
                    if (response.success) {
                        show_toastr('success', response.message);
                        $('#attachContactModal').modal('hide');
                        location.reload();
                    } else {
                        show_toastr('error', response.message);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    if (response && response.message) {
                        show_toastr('error', response.message);
                    } else {
                        show_toastr('error', 'Error attaching contacts to group');
                    }
                }
            });
        });

        // Reset modal states when closed
        $('#attachContactModal').on('hidden.bs.modal', function() {
            $('#selected-contacts-summary').hide();
            $('#attachContactsBtn').prop('disabled', true);
            $('.contact-checkbox').prop('checked', false);
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\new_laravel_project\omx-new-saas\resources\views/contact-groups/index.blade.php ENDPATH**/ ?>