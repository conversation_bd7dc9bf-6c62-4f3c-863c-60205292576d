@extends('layouts.admin')

@push('css-page')
    <style>
        .finance-tabs {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .finance-tab-nav {
            display: flex;
            flex-wrap: wrap;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0;
            margin: 0;
        }
        .finance-tab-item {
            flex: 1;
            min-width: 120px;
            text-align: center;
            border: none;
            background: transparent;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: #6c757d;
            position: relative;
        }
        .finance-tab-item:hover {
            background: #e9ecef;
            color: #495057;
        }
        .finance-tab-item.active {
            background: #fff;
            color: #0d6efd;
            border-bottom: 3px solid #0d6efd;
        }
        .finance-tab-content {
            padding: 20px;
            min-height: 500px;
        }
        .tab-pane {
            display: none;
        }
        .tab-pane.active {
            display: block;
        }
        @media (max-width: 768px) {
            .finance-tab-nav {
                flex-direction: column;
            }
            .finance-tab-item {
                flex: none;
                text-align: left;
                padding: 12px 20px;
            }
        }
    </style>
@endpush

@section('page-title')
    {{ __('Finance Management') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Finance') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-sm-12">
            <!-- Finance Tabs Navigation -->
            <div class="finance-tabs">
                <div class="finance-tab-nav">
                    <button class="finance-tab-item active" data-tab="plan">
                        <i class="ti ti-package me-2"></i>{{ __('Plan') }}
                    </button>
                    <button class="finance-tab-item" data-tab="sales">
                        <i class="ti ti-shopping-cart me-2"></i>{{ __('Sales') }}
                    </button>
                    <button class="finance-tab-item" data-tab="invoices">
                        <i class="ti ti-file-invoice me-2"></i>{{ __('Invoices') }}
                    </button>
                    <button class="finance-tab-item" data-tab="transactions">
                        <i class="ti ti-arrows-exchange me-2"></i>{{ __('Transactions') }}
                    </button>
                    <button class="finance-tab-item" data-tab="expenses">
                        <i class="ti ti-receipt me-2"></i>{{ __('Expenses') }}
                    </button>
                    <button class="finance-tab-item" data-tab="reports">
                        <i class="ti ti-chart-bar me-2"></i>{{ __('Reports') }}
                    </button>
                    <button class="finance-tab-item" data-tab="payment-gateways">
                        <i class="ti ti-credit-card me-2"></i>{{ __('Payment Gateways') }}
                    </button>
                    <button class="finance-tab-item" data-tab="business-info">
                        <i class="ti ti-building me-2"></i>{{ __('Business Info') }}
                    </button>
                </div>

                <!-- Tab Content -->
                <div class="finance-tab-content">
                    <!-- Plan Tab -->
                    <div class="tab-pane active" id="plan-tab">
                        @include('finance.tabs.plan')
                    </div>

                    <!-- Sales Tab -->
                    <div class="tab-pane" id="sales-tab">
                        @include('finance.tabs.sales')
                    </div>

                    <!-- Invoices Tab -->
                    <div class="tab-pane" id="invoices-tab">
                        @include('finance.tabs.invoices')
                    </div>

                    <!-- Transactions Tab -->
                    <div class="tab-pane" id="transactions-tab">
                        @include('finance.tabs.transactions')
                    </div>

                    <!-- Expenses Tab -->
                    <div class="tab-pane" id="expenses-tab">
                        @include('finance.tabs.expenses')
                    </div>

                    <!-- Reports Tab -->
                    <div class="tab-pane" id="reports-tab">
                        @include('finance.tabs.reports')
                    </div>

                    <!-- Payment Gateways Tab -->
                    <div class="tab-pane" id="payment-gateways-tab">
                        @include('finance.tabs.payment-gateways')
                    </div>

                    <!-- Business Info Tab -->
                    <div class="tab-pane" id="business-info-tab">
                        @include('finance.tabs.business-info')
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('script-page')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabItems = document.querySelectorAll('.finance-tab-item');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all tabs and panes
            tabItems.forEach(tab => tab.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');

            // Show corresponding tab pane
            const targetPane = document.getElementById(targetTab + '-tab');
            if (targetPane) {
                targetPane.classList.add('active');
            }

            // Store active tab in localStorage
            localStorage.setItem('activeFinanceTab', targetTab);
        });
    });

    // Restore active tab from localStorage
    const savedTab = localStorage.getItem('activeFinanceTab');
    if (savedTab) {
        const savedTabItem = document.querySelector(`[data-tab="${savedTab}"]`);
        if (savedTabItem) {
            savedTabItem.click();
        }
    }

    // Handle URL hash for direct tab access
    const hash = window.location.hash.substring(1);
    if (hash) {
        const hashTabItem = document.querySelector(`[data-tab="${hash}"]`);
        if (hashTabItem) {
            hashTabItem.click();
        }
    }

    // Update URL hash when tab changes
    tabItems.forEach(item => {
        item.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            window.history.replaceState(null, null, '#' + targetTab);
        });
    });
});
</script>
@endpush
