{{ Form::model($customField, array('route' => array('custom-field.update', $customField->id), 'method' => 'PUT', 'class'=>'needs-validation', 'novalidate')) }}
<div class="modal-body">
    <div class="row">
        <div class="form-group col-md-12">
            {{Form::label('name',__('Custom Field Name'),['class'=>'form-label'])}}<x-required></x-required>
            {{Form::text('name',null,array('class'=>'form-control','required'=>'required', 'placeholder'=>__('Enter Custom Field Name')))}}
        </div>

        <div class="form-group col-md-12">
            {{Form::label('type',__('Type'),['class'=>'form-label'])}}
            {{Form::select('type', $types, null, array('class'=>'form-control select', 'disabled' => 'disabled'))}}
        </div>

        <div class="form-group col-md-12">
            {{Form::label('module',__('Module'),['class'=>'form-label'])}}
            {{Form::select('module', $modules, null, array('class'=>'form-control select', 'disabled' => 'disabled'))}}
        </div>

        <div class="form-group col-md-12">
            {{Form::label('is_required',__('Is Required'),['class'=>'form-label'])}}
            {{Form::select('is_required', [0 => __('No'), 1 => __('Yes')], null, array('class'=>'form-control select'))}}
        </div>

        <div class="form-group col-md-12">
            {{Form::label('status',__('Status'),['class'=>'form-label'])}}
            {{Form::select('status', [1 => __('Active'), 0 => __('Inactive')], null, array('class'=>'form-control select'))}}
        </div>

        @if(in_array($customField->type, ['checkbox', 'radio', 'select', 'multiselect']))
        <div class="form-group col-md-12">
            {{Form::label('options',__('Options'),['class'=>'form-label'])}}
            <div id="options-list">
                @if($customField->options)
                    @foreach($customField->options as $option)
                        <div class="d-flex mb-2">
                            {{Form::text('options[]', $option, array('class'=>'form-control me-2', 'placeholder'=>__('Option')))}}
                            <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
                        </div>
                    @endforeach
                @endif
                <div class="d-flex mb-2">
                    {{Form::text('options[]', null, array('class'=>'form-control me-2', 'placeholder'=>__('Option')))}}
                    <button type="button" class="btn btn-sm btn-success add-option">+</button>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

    <div class="modal-footer">
        <input type="button" value="{{__('Cancel')}}" class="btn  btn-secondary" data-bs-dismiss="modal">
        <input type="submit" value="{{__('Update')}}" class="btn  btn-primary" id="update-custom-field-btn">
    </div>
{{ Form::close() }}

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const optionsList = document.getElementById('options-list');
        
        if (optionsList) {
            function addOptionInput() {
                const inputDiv = document.createElement('div');
                inputDiv.className = 'd-flex mb-2';
                inputDiv.innerHTML = `
                    <input type="text" name="options[]" class="form-control me-2" placeholder="{{ __('Option') }}">
                    <button type="button" class="btn btn-sm btn-danger remove-option">-</button>
                `;
                optionsList.appendChild(inputDiv);
            }

            optionsList.addEventListener('click', function (e) {
                if (e.target.classList.contains('remove-option')) {
                    e.target.closest('.d-flex').remove();
                }
            });

            // Add option button
            document.addEventListener('click', function (e) {
                if (e.target.classList.contains('add-option')) {
                    addOptionInput();
                }
            });
        }
    });

    // Handle form submission via AJAX
    $('form').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var submitBtn = $('#update-custom-field-btn');
        var originalText = submitBtn.val();
        
        submitBtn.val('Updating...').prop('disabled', true);
        
        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.success) {
                    // Close modal and reload page
                    $('.modal').modal('hide');
                    location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    var errorMessage = '';
                    $.each(xhr.responseJSON.errors, function(key, value) {
                        errorMessage += value[0] + '\n';
                    });
                    alert('Validation errors:\n' + errorMessage);
                } else {
                    alert('Error updating custom field');
                }
            },
            complete: function() {
                submitBtn.val(originalText).prop('disabled', false);
            }
        });
    });
</script>
