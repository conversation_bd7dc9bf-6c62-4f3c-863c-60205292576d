<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->decimal('payment_amount', 10, 2)->nullable()->after('time');
            $table->string('payment_status')->default('pending')->after('payment_amount'); // pending, paid, failed, refunded
            $table->string('payment_method')->nullable()->after('payment_status'); // razorpay, stripe, etc.
            $table->string('payment_transaction_id')->nullable()->after('payment_method');
            $table->timestamp('payment_date')->nullable()->after('payment_transaction_id');
            $table->text('payment_details')->nullable()->after('payment_date'); // JSON field for payment response
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropColumn(['payment_amount', 'payment_status', 'payment_method', 'payment_transaction_id', 'payment_date', 'payment_details']);
        });
    }
}; 