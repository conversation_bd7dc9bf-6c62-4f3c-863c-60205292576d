{"__meta": {"id": "01K1B0FYY26G4EMQF2TG4ZPRA2", "datetime": "2025-07-29 11:53:40", "utime": **********.547378, "method": "GET", "uri": "/leads", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.184406, "end": **********.547402, "duration": 1.3629958629608154, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": **********.184406, "relative_start": 0, "end": **********.589231, "relative_end": **********.589231, "duration": 0.*****************, "duration_str": "405ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.58924, "relative_start": 0.****************, "end": **********.547404, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "958ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.595958, "relative_start": 0.*****************, "end": **********.603193, "relative_end": **********.603193, "duration": 0.007235050201416016, "duration_str": "7.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.928507, "relative_start": 0.****************, "end": **********.544258, "relative_end": **********.544258, "duration": 0.****************, "duration_str": "616ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "58MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "1x leads.index", "param_count": null, "params": [], "start": **********.931197, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.phpleads.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "leads.index"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.465432, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.470536, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.526432, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.539764, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.542672, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.543432, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET leads", "middleware": "web, verified, auth, XSS", "as": "leads.index", "controller": "App\\Http\\Controllers\\LeadController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=48\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=48\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/LeadController.php:48-112</a>"}, "queries": {"count": 79, "nb_statements": 79, "nb_visible_statements": 79, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06543999999999997, "accumulated_duration_str": "65.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 74 limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.628166, "duration": 0.02036, "duration_str": "20.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_db", "explain": null, "start_percent": 0, "width_percent": 31.112}, {"sql": "select * from `settings` where `created_by` = 74", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.666857, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_db", "explain": null, "start_percent": 31.112, "width_percent": 1.238}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (74) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 50}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.674623, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 160}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_db", "explain": null, "start_percent": 32.35, "width_percent": 0.856}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (74) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 20, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 50}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.678155, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": {"index": 19, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 160}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_db", "explain": null, "start_percent": 33.206, "width_percent": 0.795}, {"sql": "select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 160}, {"index": 24, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 250}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 50}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.6825922, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "User.php:160", "source": {"index": 23, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 160}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=160", "ajax": false, "filename": "User.php", "line": "160"}, "connection": "omx_sass_db", "explain": null, "start_percent": 34.001, "width_percent": 3.133}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.716563, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "omx_sass_db", "explain": null, "start_percent": 37.133, "width_percent": 2.613}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467, 468, 469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479, 480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490, 491, 492, 493, 494, 495, 496, 497, 498, 499, 500, 501, 502, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 513, 514, 515, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 529, 530, 531, 532, 533, 534, 535, 536, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 562, 563, 564, 565, 566, 567, 568, 569, 570, 571, 572, 573, 574, 575, 576, 577, 578, 579, 580, 581, 582, 583, 584, 585, 586, 587, 588, 589, 590, 591, 592, 593, 594, 595, 596, 597, 598, 599, 600, 601, 602, 603, 604, 605, 606, 607, 608, 609, 610, 611, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 623, 624, 625, 626, 627, 628, 629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.7486432, "duration": 0.004849999999999999, "duration_str": "4.85ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "omx_sass_db", "explain": null, "start_percent": 39.746, "width_percent": 7.411}, {"sql": "select * from `pipelines` where `created_by` = 74 and `id` = 21 limit 1", "type": "query", "params": [], "bindings": [74, 21], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 57}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.9156141, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:57", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=57", "ajax": false, "filename": "LeadController.php", "line": "57"}, "connection": "omx_sass_db", "explain": null, "start_percent": 47.158, "width_percent": 0.779}, {"sql": "select * from `pipelines` where `created_by` = 74", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 84}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.91784, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:84", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 84}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=84", "ajax": false, "filename": "LeadController.php", "line": "84"}, "connection": "omx_sass_db", "explain": null, "start_percent": 47.937, "width_percent": 0.657}, {"sql": "select `id`, `name`, `email`, `type` from `users` where (`created_by` = 74 or `id` = 74) and `type` != 'client' and `is_active` = 1 order by `name` asc", "type": "query", "params": [], "bindings": [74, 74, "client", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 102}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.9200191, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "LeadController.php:102", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/LeadController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\LeadController.php", "line": 102}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=102", "ajax": false, "filename": "LeadController.php", "line": "102"}, "connection": "omx_sass_db", "explain": null, "start_percent": 48.594, "width_percent": 0.688}, {"sql": "select * from `lead_stages` where `lead_stages`.`pipeline_id` = 21 and `lead_stages`.`pipeline_id` is not null and `created_by` = 74 order by `order` asc", "type": "query", "params": [], "bindings": [21, 74], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1239}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.2494922, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "leads.index:1239", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1239}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1239", "ajax": false, "filename": "index.blade.php", "line": "1239"}, "connection": "omx_sass_db", "explain": null, "start_percent": 49.282, "width_percent": 0.993}, {"sql": "select `leads`.* from `leads` where `leads`.`created_by` = 74 and `leads`.`stage_id` = 84 and `leads`.`is_converted` = 0 order by `leads`.`order` asc", "type": "query", "params": [], "bindings": [74, 84, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/LeadStage.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadStage.php", "line": 19}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1247}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.252683, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "LeadStage.php:19", "source": {"index": 15, "namespace": null, "name": "app/Models/LeadStage.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadStage.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=19", "ajax": false, "filename": "LeadStage.php", "line": "19"}, "connection": "omx_sass_db", "explain": null, "start_percent": 50.275, "width_percent": 0.886}, {"sql": "select * from `tags` where `id` in ('87')", "type": "query", "params": [], "bindings": ["87"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 140}, {"index": 22, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1298}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.271744, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Lead.php:54", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=54", "ajax": false, "filename": "Lead.php", "line": "54"}, "connection": "omx_sass_db", "explain": null, "start_percent": 51.161, "width_percent": 0.871}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":87', '\\\"name\\\":\\\"company\\\"', '\\\"created_by\\\":74', '\\\"created_at\\\":\\\"2025-07-17T06:06:30.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-17T06:06:30.000000Z\\\"}]')", "type": "query", "params": [], "bindings": ["[{\"id\":87", "\"name\":\"company\"", "\"created_by\":74", "\"created_at\":\"2025-07-17T06:06:30.000000Z\"", "\"updated_at\":\"2025-07-17T06:06:30.000000Z\"}]"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 140}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1298}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.275938, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Lead.php:140", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=140", "ajax": false, "filename": "Lead.php", "line": "140"}, "connection": "omx_sass_db", "explain": null, "start_percent": 52.032, "width_percent": 0.733}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1306}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.295342, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Lead.php:93", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=93", "ajax": false, "filename": "Lead.php", "line": "93"}, "connection": "omx_sass_db", "explain": null, "start_percent": 52.766, "width_percent": 0.917}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1307}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.311319, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Lead.php:103", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=103", "ajax": false, "filename": "Lead.php", "line": "103"}, "connection": "omx_sass_db", "explain": null, "start_percent": 53.683, "width_percent": 0.749}, {"sql": "select `users`.*, `user_leads`.`lead_id` as `pivot_lead_id`, `user_leads`.`user_id` as `pivot_user_id` from `users` inner join `user_leads` on `users`.`id` = `user_leads`.`user_id` where `user_leads`.`lead_id` = 14", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.313714, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "leads.index:1339", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1339", "ajax": false, "filename": "index.blade.php", "line": "1339"}, "connection": "omx_sass_db", "explain": null, "start_percent": 54.432, "width_percent": 0.978}, {"sql": "select * from `tags` where `id` in ('84', '85', '88')", "type": "query", "params": [], "bindings": ["84", "85", "88"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 140}, {"index": 22, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1298}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3177378, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Lead.php:54", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=54", "ajax": false, "filename": "Lead.php", "line": "54"}, "connection": "omx_sass_db", "explain": null, "start_percent": 55.41, "width_percent": 0.627}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":84', '\\\"name\\\":\\\"In Progress\\\"', '\\\"created_by\\\":74', '\\\"created_at\\\":\\\"2025-07-14T08:26:07.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-14T08:26:07.000000Z\\\"}', '{\\\"id\\\":85', '\\\"name\\\":\\\"High\\\"', '\\\"created_by\\\":74', '\\\"created_at\\\":\\\"2025-07-14T08:26:41.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-14T08:26:41.000000Z\\\"}', '{\\\"id\\\":88', '\\\"name\\\":\\\"In Progress\\\"', '\\\"created_by\\\":74', '\\\"created_at\\\":\\\"2025-07-26T07:01:28.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-26T07:01:28.000000Z\\\"}]')", "type": "query", "params": [], "bindings": ["[{\"id\":84", "\"name\":\"In Progress\"", "\"created_by\":74", "\"created_at\":\"2025-07-14T08:26:07.000000Z\"", "\"updated_at\":\"2025-07-14T08:26:07.000000Z\"}", "{\"id\":85", "\"name\":\"High\"", "\"created_by\":74", "\"created_at\":\"2025-07-14T08:26:41.000000Z\"", "\"updated_at\":\"2025-07-14T08:26:41.000000Z\"}", "{\"id\":88", "\"name\":\"In Progress\"", "\"created_by\":74", "\"created_at\":\"2025-07-26T07:01:28.000000Z\"", "\"updated_at\":\"2025-07-26T07:01:28.000000Z\"}]"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 140}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1298}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.31994, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Lead.php:140", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=140", "ajax": false, "filename": "Lead.php", "line": "140"}, "connection": "omx_sass_db", "explain": null, "start_percent": 56.036, "width_percent": 0.688}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1306}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.321903, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Lead.php:93", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=93", "ajax": false, "filename": "Lead.php", "line": "93"}, "connection": "omx_sass_db", "explain": null, "start_percent": 56.724, "width_percent": 0.596}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1307}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3237772, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Lead.php:103", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=103", "ajax": false, "filename": "Lead.php", "line": "103"}, "connection": "omx_sass_db", "explain": null, "start_percent": 57.32, "width_percent": 0.443}, {"sql": "select `users`.*, `user_leads`.`lead_id` as `pivot_lead_id`, `user_leads`.`user_id` as `pivot_user_id` from `users` inner join `user_leads` on `users`.`id` = `user_leads`.`user_id` where `user_leads`.`lead_id` = 13", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.32566, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "leads.index:1339", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1339", "ajax": false, "filename": "index.blade.php", "line": "1339"}, "connection": "omx_sass_db", "explain": null, "start_percent": 57.763, "width_percent": 0.825}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1306}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3296862, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Lead.php:93", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=93", "ajax": false, "filename": "Lead.php", "line": "93"}, "connection": "omx_sass_db", "explain": null, "start_percent": 58.588, "width_percent": 0.947}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1307}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3320649, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Lead.php:103", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=103", "ajax": false, "filename": "Lead.php", "line": "103"}, "connection": "omx_sass_db", "explain": null, "start_percent": 59.535, "width_percent": 0.657}, {"sql": "select `users`.*, `user_leads`.`lead_id` as `pivot_lead_id`, `user_leads`.`user_id` as `pivot_user_id` from `users` inner join `user_leads` on `users`.`id` = `user_leads`.`user_id` where `user_leads`.`lead_id` = 15", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.3341348, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "leads.index:1339", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1339", "ajax": false, "filename": "index.blade.php", "line": "1339"}, "connection": "omx_sass_db", "explain": null, "start_percent": 60.193, "width_percent": 0.902}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1306}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.337959, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Lead.php:93", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=93", "ajax": false, "filename": "Lead.php", "line": "93"}, "connection": "omx_sass_db", "explain": null, "start_percent": 61.094, "width_percent": 0.611}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1307}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.339849, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Lead.php:103", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=103", "ajax": false, "filename": "Lead.php", "line": "103"}, "connection": "omx_sass_db", "explain": null, "start_percent": 61.705, "width_percent": 0.443}, {"sql": "select `users`.*, `user_leads`.`lead_id` as `pivot_lead_id`, `user_leads`.`user_id` as `pivot_user_id` from `users` inner join `user_leads` on `users`.`id` = `user_leads`.`user_id` where `user_leads`.`lead_id` = 16", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.341728, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "leads.index:1339", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1339", "ajax": false, "filename": "index.blade.php", "line": "1339"}, "connection": "omx_sass_db", "explain": null, "start_percent": 62.149, "width_percent": 0.825}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1306}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.346076, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Lead.php:93", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=93", "ajax": false, "filename": "Lead.php", "line": "93"}, "connection": "omx_sass_db", "explain": null, "start_percent": 62.974, "width_percent": 0.886}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1307}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.348778, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Lead.php:103", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=103", "ajax": false, "filename": "Lead.php", "line": "103"}, "connection": "omx_sass_db", "explain": null, "start_percent": 63.86, "width_percent": 0.902}, {"sql": "select `users`.*, `user_leads`.`lead_id` as `pivot_lead_id`, `user_leads`.`user_id` as `pivot_user_id` from `users` inner join `user_leads` on `users`.`id` = `user_leads`.`user_id` where `user_leads`.`lead_id` = 17", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.351099, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "leads.index:1339", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1339", "ajax": false, "filename": "index.blade.php", "line": "1339"}, "connection": "omx_sass_db", "explain": null, "start_percent": 64.762, "width_percent": 1.009}, {"sql": "select `leads`.* from `leads` where `leads`.`created_by` = 74 and `leads`.`stage_id` = 122 and `leads`.`is_converted` = 0 order by `leads`.`order` asc", "type": "query", "params": [], "bindings": [74, 122, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/LeadStage.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadStage.php", "line": 19}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1247}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.353404, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "LeadStage.php:19", "source": {"index": 15, "namespace": null, "name": "app/Models/LeadStage.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadStage.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=19", "ajax": false, "filename": "LeadStage.php", "line": "19"}, "connection": "omx_sass_db", "explain": null, "start_percent": 65.77, "width_percent": 0.856}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1306}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.357401, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Lead.php:93", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=93", "ajax": false, "filename": "Lead.php", "line": "93"}, "connection": "omx_sass_db", "explain": null, "start_percent": 66.626, "width_percent": 0.611}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1307}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.359296, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Lead.php:103", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=103", "ajax": false, "filename": "Lead.php", "line": "103"}, "connection": "omx_sass_db", "explain": null, "start_percent": 67.237, "width_percent": 0.458}, {"sql": "select `users`.*, `user_leads`.`lead_id` as `pivot_lead_id`, `user_leads`.`user_id` as `pivot_user_id` from `users` inner join `user_leads` on `users`.`id` = `user_leads`.`user_id` where `user_leads`.`lead_id` = 20", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.36118, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "leads.index:1339", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1339", "ajax": false, "filename": "index.blade.php", "line": "1339"}, "connection": "omx_sass_db", "explain": null, "start_percent": 67.696, "width_percent": 0.963}, {"sql": "select * from `tags` where `id` in ('89')", "type": "query", "params": [], "bindings": ["89"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 54}, {"index": 21, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 140}, {"index": 22, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1298}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.365214, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Lead.php:54", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=54", "ajax": false, "filename": "Lead.php", "line": "54"}, "connection": "omx_sass_db", "explain": null, "start_percent": 68.658, "width_percent": 0.703}, {"sql": "select * from `tags` where `id` in ('[{\\\"id\\\":89', '\\\"name\\\":\\\"Highl\\\"', '\\\"created_by\\\":74', '\\\"created_at\\\":\\\"2025-07-26T07:02:55.000000Z\\\"', '\\\"updated_at\\\":\\\"2025-07-26T07:02:55.000000Z\\\"}]')", "type": "query", "params": [], "bindings": ["[{\"id\":89", "\"name\":\"<PERSON><PERSON>\"", "\"created_by\":74", "\"created_at\":\"2025-07-26T07:02:55.000000Z\"", "\"updated_at\":\"2025-07-26T07:02:55.000000Z\"}]"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 140}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1298}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3671691, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Lead.php:140", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=140", "ajax": false, "filename": "Lead.php", "line": "140"}, "connection": "omx_sass_db", "explain": null, "start_percent": 69.361, "width_percent": 0.672}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1306}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.369099, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Lead.php:93", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=93", "ajax": false, "filename": "Lead.php", "line": "93"}, "connection": "omx_sass_db", "explain": null, "start_percent": 70.034, "width_percent": 0.611}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1307}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.370995, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Lead.php:103", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=103", "ajax": false, "filename": "Lead.php", "line": "103"}, "connection": "omx_sass_db", "explain": null, "start_percent": 70.645, "width_percent": 0.443}, {"sql": "select `users`.*, `user_leads`.`lead_id` as `pivot_lead_id`, `user_leads`.`user_id` as `pivot_user_id` from `users` inner join `user_leads` on `users`.`id` = `user_leads`.`user_id` where `user_leads`.`lead_id` = 10", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.372877, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "leads.index:1339", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1339", "ajax": false, "filename": "index.blade.php", "line": "1339"}, "connection": "omx_sass_db", "explain": null, "start_percent": 71.088, "width_percent": 0.84}, {"sql": "select * from `product_services` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1306}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.3766088, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Lead.php:93", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=93", "ajax": false, "filename": "Lead.php", "line": "93"}, "connection": "omx_sass_db", "explain": null, "start_percent": 71.928, "width_percent": 0.611}, {"sql": "select * from `sources` where 0 = 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1307}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.378518, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Lead.php:103", "source": {"index": 15, "namespace": null, "name": "app/Models/Lead.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Lead.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=103", "ajax": false, "filename": "Lead.php", "line": "103"}, "connection": "omx_sass_db", "explain": null, "start_percent": 72.54, "width_percent": 0.55}, {"sql": "select `users`.*, `user_leads`.`lead_id` as `pivot_lead_id`, `user_leads`.`user_id` as `pivot_user_id` from `users` inner join `user_leads` on `users`.`id` = `user_leads`.`user_id` where `user_leads`.`lead_id` = 19", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.3805819, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "leads.index:1339", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1339", "ajax": false, "filename": "index.blade.php", "line": "1339"}, "connection": "omx_sass_db", "explain": null, "start_percent": 73.09, "width_percent": 0.84}, {"sql": "select `leads`.* from `leads` where `leads`.`created_by` = 74 and `leads`.`stage_id` = 84 and `leads`.`is_converted` = 0 order by `leads`.`order` asc", "type": "query", "params": [], "bindings": [74, 84, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/LeadStage.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadStage.php", "line": 19}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1402}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.382747, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "LeadStage.php:19", "source": {"index": 15, "namespace": null, "name": "app/Models/LeadStage.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadStage.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=19", "ajax": false, "filename": "LeadStage.php", "line": "19"}, "connection": "omx_sass_db", "explain": null, "start_percent": 73.93, "width_percent": 0.779}, {"sql": "select * from `lead_activity_logs` where `lead_activity_logs`.`lead_id` = 14 and `lead_activity_logs`.`lead_id` is not null order by `id` desc", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.408027, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "leads.index:1415", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1415", "ajax": false, "filename": "index.blade.php", "line": "1415"}, "connection": "omx_sass_db", "explain": null, "start_percent": 74.71, "width_percent": 0.932}, {"sql": "select * from `lead_activity_logs` where `lead_activity_logs`.`lead_id` = 13 and `lead_activity_logs`.`lead_id` is not null order by `id` desc", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.4100618, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "leads.index:1415", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1415", "ajax": false, "filename": "index.blade.php", "line": "1415"}, "connection": "omx_sass_db", "explain": null, "start_percent": 75.642, "width_percent": 0.657}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4142761, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 76.299, "width_percent": 1.238}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.425359, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 77.537, "width_percent": 1.009}, {"sql": "select * from `lead_activity_logs` where `lead_activity_logs`.`lead_id` = 15 and `lead_activity_logs`.`lead_id` is not null order by `id` desc", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.427725, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "leads.index:1415", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1415", "ajax": false, "filename": "index.blade.php", "line": "1415"}, "connection": "omx_sass_db", "explain": null, "start_percent": 78.545, "width_percent": 0.672}, {"sql": "select * from `lead_activity_logs` where `lead_activity_logs`.`lead_id` = 16 and `lead_activity_logs`.`lead_id` is not null order by `id` desc", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.4296508, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "leads.index:1415", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1415", "ajax": false, "filename": "index.blade.php", "line": "1415"}, "connection": "omx_sass_db", "explain": null, "start_percent": 79.218, "width_percent": 0.657}, {"sql": "select * from `lead_activity_logs` where `lead_activity_logs`.`lead_id` = 17 and `lead_activity_logs`.`lead_id` is not null order by `id` desc", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.431398, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "leads.index:1415", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1415", "ajax": false, "filename": "index.blade.php", "line": "1415"}, "connection": "omx_sass_db", "explain": null, "start_percent": 79.875, "width_percent": 0.627}, {"sql": "select `leads`.* from `leads` where `leads`.`created_by` = 74 and `leads`.`stage_id` = 122 and `leads`.`is_converted` = 0 order by `leads`.`order` asc", "type": "query", "params": [], "bindings": [74, 122, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/LeadStage.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadStage.php", "line": 19}, {"index": 16, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1402}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.433155, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "LeadStage.php:19", "source": {"index": 15, "namespace": null, "name": "app/Models/LeadStage.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadStage.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=19", "ajax": false, "filename": "LeadStage.php", "line": "19"}, "connection": "omx_sass_db", "explain": null, "start_percent": 80.501, "width_percent": 0.779}, {"sql": "select * from `lead_activity_logs` where `lead_activity_logs`.`lead_id` = 20 and `lead_activity_logs`.`lead_id` is not null order by `id` desc", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.435243, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "leads.index:1415", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1415", "ajax": false, "filename": "index.blade.php", "line": "1415"}, "connection": "omx_sass_db", "explain": null, "start_percent": 81.281, "width_percent": 0.627}, {"sql": "select * from `lead_activity_logs` where `lead_activity_logs`.`lead_id` = 10 and `lead_activity_logs`.`lead_id` is not null order by `id` desc", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.4369829, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "leads.index:1415", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1415", "ajax": false, "filename": "index.blade.php", "line": "1415"}, "connection": "omx_sass_db", "explain": null, "start_percent": 81.907, "width_percent": 0.627}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.438776, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 82.534, "width_percent": 0.749}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.44079, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 83.282, "width_percent": 0.657}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4428332, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 83.939, "width_percent": 0.718}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.444824, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 84.658, "width_percent": 0.733}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.446937, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 85.391, "width_percent": 0.749}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.448945, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 86.14, "width_percent": 0.718}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.450886, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 86.858, "width_percent": 0.703}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.452824, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 87.561, "width_percent": 0.703}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4547832, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 88.264, "width_percent": 0.703}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4567342, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 88.967, "width_percent": 0.703}, {"sql": "select * from `users` where `users`.`id` = 74 and `users`.`id` is not null limit 1", "type": "query", "params": [], "bindings": [74], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, {"index": 22, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 19}, {"index": 23, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1426}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.45868, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "LeadActivityLog.php:33", "source": {"index": 21, "namespace": null, "name": "app/Models/LeadActivityLog.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\LeadActivityLog.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=33", "ajax": false, "filename": "LeadActivityLog.php", "line": "33"}, "connection": "omx_sass_db", "explain": null, "start_percent": 89.67, "width_percent": 0.581}, {"sql": "select * from `lead_activity_logs` where `lead_activity_logs`.`lead_id` = 19 and `lead_activity_logs`.`lead_id` is not null order by `id` desc", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.4610012, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "leads.index:1415", "source": {"index": 20, "namespace": "view", "name": "leads.index", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/leads/index.blade.php", "line": 1415}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fleads%2Findex.blade.php&line=1415", "ajax": false, "filename": "index.blade.php", "line": "1415"}, "connection": "omx_sass_db", "explain": null, "start_percent": 90.251, "width_percent": 0.749}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.466138, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 90.999, "width_percent": 0.902}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.468216, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 91.901, "width_percent": 0.611}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.473383, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 92.512, "width_percent": 0.611}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.475646, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": {"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\EmailTemplate.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "omx_sass_db", "explain": null, "start_percent": 93.123, "width_percent": 0.428}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.4776711, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "partials.admin.menu:20", "source": {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=20", "ajax": false, "filename": "menu.blade.php", "line": "20"}, "connection": "omx_sass_db", "explain": null, "start_percent": 93.551, "width_percent": 0.947}, {"sql": "select * from `pricing_plans` where `pricing_plans`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 211}, {"index": 22, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 757}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.512796, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "User.php:211", "source": {"index": 21, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 211}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=211", "ajax": false, "filename": "User.php", "line": "211"}, "connection": "omx_sass_db", "explain": null, "start_percent": 94.499, "width_percent": 0.764}, {"sql": "select * from `module_integrations` where `name` = 'Automatish' and `enabled` = 1 limit 1", "type": "query", "params": [], "bindings": ["Automatish", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 912}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.5212169, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "partials.admin.menu:912", "source": {"index": 16, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 912}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=912", "ajax": false, "filename": "menu.blade.php", "line": "912"}, "connection": "omx_sass_db", "explain": null, "start_percent": 95.263, "width_percent": 0.611}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.527018, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 95.874, "width_percent": 0.718}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'omx_sass_db' and table_name = 'languages' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 6}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.529088, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "omx_sass_db", "explain": null, "start_percent": 96.592, "width_percent": 0.902}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 6}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.531926, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "omx_sass_db", "explain": null, "start_percent": 97.494, "width_percent": 0.657}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 16}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.534554, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 98.151, "width_percent": 0.611}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 74 and `seen` = 0", "type": "query", "params": [], "bindings": [74, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.536825, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:20", "source": {"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=20", "ajax": false, "filename": "header.blade.php", "line": "20"}, "connection": "omx_sass_db", "explain": null, "start_percent": 98.762, "width_percent": 0.627}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 5748}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.540241, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 99.389, "width_percent": 0.611}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"retrieved": 1601, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"retrieved": 1194, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 32, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Lead": {"retrieved": 16, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLead.php&line=1", "ajax": false, "filename": "Lead.php", "line": "?"}}, "App\\Models\\LeadActivityLog": {"retrieved": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadActivityLog.php&line=1", "ajax": false, "filename": "LeadActivityLog.php", "line": "?"}}, "App\\Models\\Tag": {"retrieved": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FTag.php&line=1", "ajax": false, "filename": "Tag.php", "line": "?"}}, "App\\Models\\Pipeline": {"retrieved": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FPipeline.php&line=1", "ajax": false, "filename": "Pipeline.php", "line": "?"}}, "App\\Models\\LeadStage": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FLeadStage.php&line=1", "ajax": false, "filename": "LeadStage.php", "line": "?"}}, "App\\Models\\PricingPlan": {"retrieved": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FPricingPlan.php&line=1", "ajax": false, "filename": "PricingPlan.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}}, "count": 2870, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 2870}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 84, "messages": [{"message": "[\n  ability => manage lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1348812885 data-indent-pad=\"  \"><span class=sf-dump-note>manage lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348812885\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.894208, "xdebug_link": null}, {"message": "[\n  ability => view lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1057603656 data-indent-pad=\"  \"><span class=sf-dump-note>view lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1057603656\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.255536, "xdebug_link": null}, {"message": "[\n  ability => edit lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1772960061 data-indent-pad=\"  \"><span class=sf-dump-note>edit lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1772960061\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.256161, "xdebug_link": null}, {"message": "[\n  ability => delete lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-881400866 data-indent-pad=\"  \"><span class=sf-dump-note>delete lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881400866\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.25694, "xdebug_link": null}, {"message": "[\n  ability => view lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-328996603 data-indent-pad=\"  \"><span class=sf-dump-note>view lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-328996603\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316337, "xdebug_link": null}, {"message": "[\n  ability => edit lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-674252966 data-indent-pad=\"  \"><span class=sf-dump-note>edit lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674252966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316965, "xdebug_link": null}, {"message": "[\n  ability => delete lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1303757918 data-indent-pad=\"  \"><span class=sf-dump-note>delete lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303757918\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.317527, "xdebug_link": null}, {"message": "[\n  ability => view lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1747939286 data-indent-pad=\"  \"><span class=sf-dump-note>view lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1747939286\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.328187, "xdebug_link": null}, {"message": "[\n  ability => edit lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1102782837 data-indent-pad=\"  \"><span class=sf-dump-note>edit lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102782837\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.328788, "xdebug_link": null}, {"message": "[\n  ability => delete lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1802766308 data-indent-pad=\"  \"><span class=sf-dump-note>delete lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802766308\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.329408, "xdebug_link": null}, {"message": "[\n  ability => view lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-25441026 data-indent-pad=\"  \"><span class=sf-dump-note>view lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25441026\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.336586, "xdebug_link": null}, {"message": "[\n  ability => edit lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2016586924 data-indent-pad=\"  \"><span class=sf-dump-note>edit lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016586924\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.337181, "xdebug_link": null}, {"message": "[\n  ability => delete lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-869595763 data-indent-pad=\"  \"><span class=sf-dump-note>delete lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869595763\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.337738, "xdebug_link": null}, {"message": "[\n  ability => view lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1294106511 data-indent-pad=\"  \"><span class=sf-dump-note>view lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294106511\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.344356, "xdebug_link": null}, {"message": "[\n  ability => edit lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1364771932 data-indent-pad=\"  \"><span class=sf-dump-note>edit lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1364771932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.345082, "xdebug_link": null}, {"message": "[\n  ability => delete lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1238995687 data-indent-pad=\"  \"><span class=sf-dump-note>delete lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238995687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.345741, "xdebug_link": null}, {"message": "[\n  ability => view lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-389455077 data-indent-pad=\"  \"><span class=sf-dump-note>view lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-389455077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.356027, "xdebug_link": null}, {"message": "[\n  ability => edit lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1047211756 data-indent-pad=\"  \"><span class=sf-dump-note>edit lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047211756\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.356628, "xdebug_link": null}, {"message": "[\n  ability => delete lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1668578213 data-indent-pad=\"  \"><span class=sf-dump-note>delete lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1668578213\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.357184, "xdebug_link": null}, {"message": "[\n  ability => view lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-929533049 data-indent-pad=\"  \"><span class=sf-dump-note>view lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-929533049\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.363827, "xdebug_link": null}, {"message": "[\n  ability => edit lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-755460097 data-indent-pad=\"  \"><span class=sf-dump-note>edit lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755460097\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.364422, "xdebug_link": null}, {"message": "[\n  ability => delete lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-326061101 data-indent-pad=\"  \"><span class=sf-dump-note>delete lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-326061101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.364963, "xdebug_link": null}, {"message": "[\n  ability => view lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-201409238 data-indent-pad=\"  \"><span class=sf-dump-note>view lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">view lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-201409238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.37525, "xdebug_link": null}, {"message": "[\n  ability => edit lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1225768295 data-indent-pad=\"  \"><span class=sf-dump-note>edit lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225768295\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.37583, "xdebug_link": null}, {"message": "[\n  ability => delete lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1721025177 data-indent-pad=\"  \"><span class=sf-dump-note>delete lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721025177\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.376401, "xdebug_link": null}, {"message": "[\n  ability => show hrm dashboard,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.481142, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.48187, "xdebug_link": null}, {"message": "[\n  ability => show crm dashboard,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>show crm dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show crm dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.482262, "xdebug_link": null}, {"message": "[\n  ability => statement report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-580781098 data-indent-pad=\"  \"><span class=sf-dump-note>statement report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580781098\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.483209, "xdebug_link": null}, {"message": "[\n  ability => invoice report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-436011287 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436011287\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.483635, "xdebug_link": null}, {"message": "[\n  ability => bill report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-540200854 data-indent-pad=\"  \"><span class=sf-dump-note>bill report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-540200854\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.484103, "xdebug_link": null}, {"message": "[\n  ability => stock report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-67870413 data-indent-pad=\"  \"><span class=sf-dump-note>stock report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67870413\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.484505, "xdebug_link": null}, {"message": "[\n  ability => loss & profit report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-533190062 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-533190062\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.484916, "xdebug_link": null}, {"message": "[\n  ability => manage transaction,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-262046868 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-262046868\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.485299, "xdebug_link": null}, {"message": "[\n  ability => income report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-303782768 data-indent-pad=\"  \"><span class=sf-dump-note>income report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-303782768\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.485698, "xdebug_link": null}, {"message": "[\n  ability => expense report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1443326166 data-indent-pad=\"  \"><span class=sf-dump-note>expense report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1443326166\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.486113, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1600539649 data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1600539649\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.486512, "xdebug_link": null}, {"message": "[\n  ability => tax report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1993217808 data-indent-pad=\"  \"><span class=sf-dump-note>tax report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1993217808\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.486914, "xdebug_link": null}, {"message": "[\n  ability => manage budget plan,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage budget plan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage budget plan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.488742, "xdebug_link": null}, {"message": "[\n  ability => manage bank account,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.489128, "xdebug_link": null}, {"message": "[\n  ability => manage customer,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.489498, "xdebug_link": null}, {"message": "[\n  ability => manage customer,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-422478145 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-422478145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.489832, "xdebug_link": null}, {"message": "[\n  ability => manage proposal,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1538287089 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1538287089\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.490279, "xdebug_link": null}, {"message": "[\n  ability => manage invoice,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1618327545 data-indent-pad=\"  \"><span class=sf-dump-note>manage invoice </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage invoice</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1618327545\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.490577, "xdebug_link": null}, {"message": "[\n  ability => manage revenue,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1273037213 data-indent-pad=\"  \"><span class=sf-dump-note>manage revenue </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage revenue</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1273037213\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.490949, "xdebug_link": null}, {"message": "[\n  ability => manage credit note,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-357689955 data-indent-pad=\"  \"><span class=sf-dump-note>manage credit note </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage credit note</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-357689955\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.491373, "xdebug_link": null}, {"message": "[\n  ability => manage vender,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1078855630 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078855630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.491714, "xdebug_link": null}, {"message": "[\n  ability => manage vender,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-583724188 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-583724188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.492057, "xdebug_link": null}, {"message": "[\n  ability => manage bill,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-65024695 data-indent-pad=\"  \"><span class=sf-dump-note>manage bill </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage bill</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65024695\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.492437, "xdebug_link": null}, {"message": "[\n  ability => manage payment,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1374214962 data-indent-pad=\"  \"><span class=sf-dump-note>manage payment </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage payment</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374214962\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.492839, "xdebug_link": null}, {"message": "[\n  ability => manage debit note,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage debit note </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage debit note</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.493279, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.493755, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.494236, "xdebug_link": null}, {"message": "[\n  ability => manage journal entry,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage journal entry </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">manage journal entry</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.494728, "xdebug_link": null}, {"message": "[\n  ability => ledger report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1340381496 data-indent-pad=\"  \"><span class=sf-dump-note>ledger report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">ledger report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1340381496\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.495334, "xdebug_link": null}, {"message": "[\n  ability => bill report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1474174042 data-indent-pad=\"  \"><span class=sf-dump-note>bill report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474174042\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.495953, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1629561105 data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1629561105\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.496487, "xdebug_link": null}, {"message": "[\n  ability => trial balance report,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1225197282 data-indent-pad=\"  \"><span class=sf-dump-note>trial balance report </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">trial balance report</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225197282\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.497019, "xdebug_link": null}, {"message": "[\n  ability => manage goal,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage goal </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.497542, "xdebug_link": null}, {"message": "[\n  ability => manage constant tax,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1180933449 data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180933449\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.497875, "xdebug_link": null}, {"message": "[\n  ability => manage print settings,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.49817, "xdebug_link": null}, {"message": "[\n  ability => manage customer,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-363085028 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-363085028\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.498619, "xdebug_link": null}, {"message": "[\n  ability => manage lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1552432716 data-indent-pad=\"  \"><span class=sf-dump-note>manage lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552432716\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.499454, "xdebug_link": null}, {"message": "[\n  ability => manage lead,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2122203641 data-indent-pad=\"  \"><span class=sf-dump-note>manage lead </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage lead</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2122203641\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.500102, "xdebug_link": null}, {"message": "[\n  ability => manage form builder,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1304771809 data-indent-pad=\"  \"><span class=sf-dump-note>manage form builder </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage form builder</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1304771809\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.501362, "xdebug_link": null}, {"message": "[\n  ability => manage contract,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1612897028 data-indent-pad=\"  \"><span class=sf-dump-note>manage contract </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage contract</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612897028\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.502503, "xdebug_link": null}, {"message": "[\n  ability => manage pipeline,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2065312798 data-indent-pad=\"  \"><span class=sf-dump-note>manage pipeline </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pipeline</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2065312798\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.503055, "xdebug_link": null}, {"message": "[\n  ability => manage lead stage,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1358681094 data-indent-pad=\"  \"><span class=sf-dump-note>manage lead stage </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage lead stage</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1358681094\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.503598, "xdebug_link": null}, {"message": "[\n  ability => manage booking,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-175288031 data-indent-pad=\"  \"><span class=sf-dump-note>manage booking </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage booking</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-175288031\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.505124, "xdebug_link": null}, {"message": "[\n  ability => manage project,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-682092277 data-indent-pad=\"  \"><span class=sf-dump-note>manage project </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage project</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-682092277\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.506176, "xdebug_link": null}, {"message": "[\n  ability => manage personal task,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1627004148 data-indent-pad=\"  \"><span class=sf-dump-note>manage personal task </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">manage personal task</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627004148\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.507803, "xdebug_link": null}, {"message": "[\n  ability => manage project task,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1272198770 data-indent-pad=\"  \"><span class=sf-dump-note>manage project task </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage project task</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272198770\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.509059, "xdebug_link": null}, {"message": "[\n  ability => manage client,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1458765545 data-indent-pad=\"  \"><span class=sf-dump-note>manage client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458765545\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.509735, "xdebug_link": null}, {"message": "[\n  ability => manage user,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-319668709 data-indent-pad=\"  \"><span class=sf-dump-note>manage user </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319668709\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.51, "xdebug_link": null}, {"message": "[\n  ability => manage employee,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1981571590 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1981571590\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.510616, "xdebug_link": null}, {"message": "[\n  ability => manage client,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-990678702 data-indent-pad=\"  \"><span class=sf-dump-note>manage client </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-990678702\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.511137, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-554058806 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554058806\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.511489, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1311165453 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1311165453\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.511803, "xdebug_link": null}, {"message": "[\n  ability => access omx flow,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-965574222 data-indent-pad=\"  \"><span class=sf-dump-note>access omx flow </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">access omx flow</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-965574222\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.518477, "xdebug_link": null}, {"message": "[\n  ability => access automatish,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1742761012 data-indent-pad=\"  \"><span class=sf-dump-note>access automatish </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">access automatish</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1742761012\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.520719, "xdebug_link": null}, {"message": "[\n  ability => manage company plan,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1838259855 data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1838259855\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.524036, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1928121970 data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1928121970\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.524359, "xdebug_link": null}, {"message": "[\n  ability => manage pricing plan,\n  target => null,\n  result => null,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage pricing plan </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage pricing plan</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.525599, "xdebug_link": null}, {"message": "[\n  ability => manage order,\n  target => null,\n  result => true,\n  user => 74,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage order </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>74</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.526011, "xdebug_link": null}]}, "session": {"_token": "c6eP6ECVIIXFVqfDkitL4uf1ArDPoRjmLfglX9lj", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/account-dashboard\"\n]", "impersonated_by": "7", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "74"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/leads", "action_name": "leads.index", "controller_action": "App\\Http\\Controllers\\LeadController@index", "uri": "GET leads", "controller": "App\\Http\\Controllers\\LeadController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=48\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FLeadController.php&line=48\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/LeadController.php:48-112</a>", "middleware": "web, verified, auth, XSS", "duration": "1.37s", "peak_memory": "66MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2127154782 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2127154782\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-190035693 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-190035693\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1985903788 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjZVREVBbC9XSnIwcnNtSE5nWGdkbWc9PSIsInZhbHVlIjoiSUZqRHlsTHl1NWFFNVYzbTdnRFhIdW5DUjY0RVFjV01mOXFNSnU5aFpBcnpsV3BWT21OZUwyM0pVMWxNT2VYZUlEa1A2WHU4cDYveHF6NVF3N1ZnV3lNY2pLM1BLUkJOMlZJZXJ0SUxnTjJJSC9BYmNKbHp1V1FlWGZNL3IweVM4Mi9jcUw4U3ZBYnZoY2JaRTkzbGo2L2pSUlVxMGk3b0wxUWZ2d2JkSUlwTDZnbUFsc3BRSGh2ZVRnZFBiV1o5aTlZcXlncElWVDA2VXdhei8wVEE2bXQzWkpUbSthTDI4eDNOaGtja0ZMVT0iLCJtYWMiOiJmYzk1YjdhMmFhYmZjYTM1ZjAxZWM4NTA5NWEyNWRmYjE1OWUxYjlkMTQ1NzUzMTY5NDM2YTk0Y2EyMDc5ZmI3IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlRXT2pSVlNYZ1hGNzZiTlhzU3Z5T2c9PSIsInZhbHVlIjoib3Z0Z3dJM2N5c2tHWmt4T1FQcjlXUG8wSlFwZGdCQXdGaXA0VForeTdZaWd0Q3dvZUJvSDlrbUxoOTZQSXR3OVhYSGVIVENXVytEdE0zOUZmWS82KzJyb3lneU5EYmIyUm5JN0hzbDk3K2FBY0w0ODEyaEhkZzRjSXAxdDBlWWZIRURtc2R1SjJ0WjNKYnNSVFc3SkNtbjFMZW1KUGtKMjR0T1VkdmwxV3grMHVwQnZzcXc3YTlKMTNGcU1Dd2ZYK1hPeFBoaktMOWg5OGIzZGhEd0xGQzNDU0lFcmNaa2ttQUpad2V0ci9sS1FEZTlYeWNSQ0FEY1FJSXN0T0JNSEQ4T0NEYTRQMjVGQXQvbFd0WXZoQUdzZmhyenhmNzZ1NWcwaE5JVnJqV0o0YW02S0F6VVBldUxmejZlVk9IL2VUNFV2YVdOVFowQmNqWU91ZHEwbTdCZmpPa0hqWnVJaE5GUlN4azRhT0ZNVVJMME9HdmNPSWxMTTZUYjl3TERuczRuYjBlUkM1RVplYXlTd1hubE5rZG5DVXA1bUs1MDJ5YnQ4SEV1WWRLMHl2WlgzdWUyV05PRkhLK0NMUENBWUo5UllTU0JKaS9raVlLOTNoS3VQcEV1TG45czRmSnJxWENFazhEWmJHb2dhZEYxK3VtMUJkTm5jTTdZUVBzNU4iLCJtYWMiOiI3MWM2YmQwNGZlZWI5YzRhYWEyZDRiOWEzMTAxZTAxOGFiYWMzZjQxODY3ZTExODQ4M2RkZDJmNGUzMDY2MGQ0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkE0UDRJQlhQYzRPL1lNK2VGQ0R3S0E9PSIsInZhbHVlIjoibnNUWDRxeDY0TnBPeU1RamR0S29VdzFtbVJmSFdrTFlvaVZVY3VyTytzYVI5Mzl5UURSWmJmd0NBTXU2NTRXaWVHZmVoeHJVZUhyemNpVCtiblU4VkUyb3lDN2tvSDZzc1hOTE4rVXNIMlZ4NS9ZZjJFMVBSeGZ3emNHTWxmdURkK2pZak42a0x3VEIyNHNUenpqMU1TNVRnemowSEh2YXFqMXFGMU9qai9mVUpNa3ROQTkvSHF1aVQ0T0hEdDRscWgzcW1hTWl2QUVXd09iKzk2d0U5ZG4ydDV1VGpNc1Nyb29UZkZlWkY4aXlTbysxeFFoaGE5YUNhNml4Ykt5YUFRcXc3SjFVL1Q5S1FGdVErc2lrc3RJbXA0YWJ0dkhLZWVEWkhFMzdEQk5heDdqcmJXUDFaWSt5VlUrVXdoZjJoaHE0NjRIVUxDVk1zQVh4WUhweDJRdzN3RTBqSDdOSHh4VEV1bUc3Z3c4YmRIZE1ZeU5rSDJvZVlGVmtKVnRlZlRuVVFuSW1XV0c0ajRrQmt0dTRxUjRLWnVia3R1enN5QzROQ0ZvZHBXaGVYY0ZyaTY5VUc2SmJCcWFtaEc3dkNSbFdCVnMrbUdkTmxFVzg2ekhzaGhqRXpoSEV0TG1ETmhwUDhQcFpZNSt1MmRRMXN5YXpoek41VnZSNS9Bd1IiLCJtYWMiOiI0MTE1MmFlY2E4MmQ2NDBiMDUzMTdkNGIxYzg4ZjVmZjM4ZWUwNWMxNDlkYWE0MTNlOTRlZDdjZTA3NjgzYjc4IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6IlcxSWdVTEJMTGJTalJ4TXoyQzhWdmc9PSIsInZhbHVlIjoiaGRsTTVZM1ZiRXRCSWZySlROdHNYNVlnQzQ2U1pkcld1UlFjODZ3aU5NcFREYU9VY0RQS3plZzFIbFZxNGJIMVBVVU9WdDYvRFRGWWVNakdIRThXdS9FSE1odG1Pc29NZGk3MjhlRE1JTEtBbUthTFhsVDByeDJPMW9kVmZoc1hwS3BhcW1yZXVTalBxZzJ4WnZxSkFTa0QvVHd4ekx5bVM2UVJTazlXUFg0SDNLQUNFZ3FKeERGS0JoWXRycmpNQ2NsNDJrWGF1MktxZ1d2UWRiTlZ2V1Vmb04rQ3pvNW5vclpmT2h6UVR6K2pSVzhhSFhHZXNua1FzSHVadFZxN0daeFRhWmpZTldFWGJzdVFLd0hGam5nSm5DL2NNK3VyUTNDdVNSSGorSjdJVndiTUVwc25BVVI0eGhFRyt3aFZiUlphNFNUTWhYOEpoM3h4RE82QURUQUZzT25hMlBKZm11THNmTVYwVXN1VEdwWXlxdzljbzFxdFdDcW4yclVtWkd0TUFYTThPY0NkLzlVcmlzeTVpKzBvYWorMllWZGcyWUZCdlllMmdQTDFPV0dTdjFkUXFQV3ZpcUNNUGZISzJiOTNOVWtwemt5YzhPV2tTWlFrRDlJRHBhL09kU0lkNjNHNFhxbi9wQmc5dnpzYVVRaktQZ1Bqalg1U3RSbSsiLCJtYWMiOiI0Yjc2NjQ2MGNiMjZhNzZmMTQxYWM0Zjg3NWU1NDMwZDRhNDYwMjBhNTJiYmY5OTI3MTdiODgyZjVjYzUzOTNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985903788\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1073038795 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vqOMknY5lq3SnPYkhHTmzjHvgrfuJ3OuOsNTzzzA</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c6eP6ECVIIXFVqfDkitL4uf1ArDPoRjmLfglX9lj</span>\"\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kWrcfTjCL8MxHY4tTylESI5sjjMcRCglPG5z0ltT</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073038795\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-37205168 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 11:53:39 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-37205168\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c6eP6ECVIIXFVqfDkitL4uf1ArDPoRjmLfglX9lj</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">http://127.0.0.1:8000/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>74</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/leads", "action_name": "leads.index", "controller_action": "App\\Http\\Controllers\\LeadController@index"}, "badge": null}}