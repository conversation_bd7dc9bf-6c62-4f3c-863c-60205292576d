<!-- Invoices Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ __('Invoice Management') }}</h4>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-info active" data-invoice-view="invoices">
                    <i class="ti ti-file-invoice me-1"></i>{{ __('Invoices') }}
                </button>
                <button type="button" class="btn btn-outline-info" data-invoice-view="quotes">
                    <i class="ti ti-file-description me-1"></i>{{ __('Quotes') }}
                </button>
                <button type="button" class="btn btn-outline-info" data-invoice-view="setup">
                    <i class="ti ti-settings me-1"></i>{{ __('Setup') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Invoices View -->
<div id="invoices-view" class="invoice-view active">
    <div class="row mb-4">
        <!-- Invoice Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-primary text-white">
                <div class="card-body text-center">
                    <i class="ti ti-file-invoice" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \App\Models\Invoice::where('created_by', \Auth::user()->creatorId())->count() }}</h4>
                    <small>{{ __('Total Invoices') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <i class="ti ti-check" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \App\Models\Invoice::where('created_by', \Auth::user()->creatorId())->where('status', 'Paid')->count() }}</h4>
                    <small>{{ __('Paid Invoices') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \App\Models\Invoice::where('created_by', \Auth::user()->creatorId())->where('status', 'Sent')->count() }}</h4>
                    <small>{{ __('Pending Invoices') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-danger text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock-exclamation" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">0</h4>
                    <small>{{ __('Overdue Invoices') }}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Recent Invoices') }}</h5>
                    <div class="d-flex gap-2">
                        @can('create invoice')
                            <a href="{{ route('invoice.create', ['cid' => 0]) }}" class="btn btn-success btn-sm">
                                <i class="ti ti-plus me-1"></i>{{ __('Create Invoice') }}
                            </a>
                        @endcan
                        <a href="{{ route('invoice.index') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-eye me-1"></i>{{ __('View All') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('Invoice') }}</th>
                                    <th>{{ __('Customer') }}</th>
                                    <th>{{ __('Issue Date') }}</th>
                                    <th>{{ __('Due Date') }}</th>
                                    <th>{{ __('Amount') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $invoices = \App\Models\Invoice::where('created_by', \Auth::user()->creatorId())->latest()->limit(5)->get();
                                @endphp
                                @forelse($invoices as $invoice)
                                <tr>
                                    <td>
                                        <a href="{{ route('invoice.show', $invoice->id) }}" class="text-decoration-none">
                                            {{ \App\Models\Invoice::invoiceNumberFormat($invoice->invoice_id) }}
                                        </a>
                                    </td>
                                    <td>{{ !empty($invoice->customer) ? $invoice->customer->name : '' }}</td>
                                    <td>{{ \Auth::user()->dateFormat($invoice->issue_date) }}</td>
                                    <td>{{ \Auth::user()->dateFormat($invoice->due_date) }}</td>
                                    <td>{{ \Auth::user()->priceFormat($invoice->getTotal()) }}</td>
                                    <td>
                                        @if($invoice->status == 'Draft')
                                            <span class="badge bg-secondary">{{ __('Draft') }}</span>
                                        @elseif($invoice->status == 'Sent')
                                            <span class="badge bg-warning">{{ __('Sent') }}</span>
                                        @elseif($invoice->status == 'Paid')
                                            <span class="badge bg-success">{{ __('Paid') }}</span>
                                        @elseif($invoice->status == 'Cancelled')
                                            <span class="badge bg-danger">{{ __('Cancelled') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('invoice.show', $invoice->id) }}" class="btn btn-sm btn-outline-primary" title="{{ __('View') }}">
                                                <i class="ti ti-eye"></i>
                                            </a>
                                            @can('edit invoice')
                                                <a href="{{ route('invoice.edit', $invoice->id) }}" class="btn btn-sm btn-outline-secondary" title="{{ __('Edit') }}">
                                                    <i class="ti ti-edit"></i>
                                                </a>
                                            @endcan
                                            <a href="{{ route('invoice.pdf', $invoice->id) }}" class="btn btn-sm btn-outline-info" title="{{ __('Download') }}">
                                                <i class="ti ti-download"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-file-invoice" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3">{{ __('No Invoices Found') }}</h5>
                                            <p>{{ __('Create your first invoice to get started') }}</p>
                                            @can('create invoice')
                                                <a href="{{ route('invoice.create', ['cid' => 0]) }}" class="btn btn-primary">
                                                    <i class="ti ti-plus me-1"></i>{{ __('Create Invoice') }}
                                                </a>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quotes View -->
<div id="quotes-view" class="invoice-view" style="display: none;">
    <div class="row mb-4">
        <!-- Quote Stats -->
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-info text-white">
                <div class="card-body text-center">
                    <i class="ti ti-file-description" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \App\Models\Proposal::where('created_by', \Auth::user()->creatorId())->count() }}</h4>
                    <small>{{ __('Total Quotes') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-success text-white">
                <div class="card-body text-center">
                    <i class="ti ti-check" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \App\Models\Proposal::where('created_by', \Auth::user()->creatorId())->where('status', 'Accepted')->count() }}</h4>
                    <small>{{ __('Accepted Quotes') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-warning text-white">
                <div class="card-body text-center">
                    <i class="ti ti-clock" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \App\Models\Proposal::where('created_by', \Auth::user()->creatorId())->where('status', 'Open')->count() }}</h4>
                    <small>{{ __('Pending Quotes') }}</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 bg-danger text-white">
                <div class="card-body text-center">
                    <i class="ti ti-x" style="font-size: 2rem;"></i>
                    <h4 class="mt-2 mb-1">{{ \App\Models\Proposal::where('created_by', \Auth::user()->creatorId())->where('status', 'Rejected')->count() }}</h4>
                    <small>{{ __('Rejected Quotes') }}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ __('Recent Quotes') }}</h5>
                    <div class="d-flex gap-2">
                        @can('create proposal')
                            <a href="{{ route('proposal.create') }}" class="btn btn-success btn-sm">
                                <i class="ti ti-plus me-1"></i>{{ __('Create Quote') }}
                            </a>
                        @endcan
                        <a href="{{ route('proposal.index') }}" class="btn btn-primary btn-sm">
                            <i class="ti ti-eye me-1"></i>{{ __('View All') }}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('Quote') }}</th>
                                    <th>{{ __('Customer') }}</th>
                                    <th>{{ __('Issue Date') }}</th>
                                    <th>{{ __('Amount') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $proposals = \App\Models\Proposal::where('created_by', \Auth::user()->creatorId())->latest()->limit(5)->get();
                                @endphp
                                @forelse($proposals as $proposal)
                                <tr>
                                    <td>
                                        <a href="{{ route('proposal.show', $proposal->id) }}" class="text-decoration-none">
                                            {{ \App\Models\Proposal::proposalNumberFormat($proposal->proposal_id) }}
                                        </a>
                                    </td>
                                    <td>{{ !empty($proposal->customer) ? $proposal->customer->name : '' }}</td>
                                    <td>{{ \Auth::user()->dateFormat($proposal->issue_date) }}</td>
                                    <td>{{ \Auth::user()->priceFormat($proposal->getTotal()) }}</td>
                                    <td>
                                        @if($proposal->status == 'Open')
                                            <span class="badge bg-warning">{{ __('Open') }}</span>
                                        @elseif($proposal->status == 'Accepted')
                                            <span class="badge bg-success">{{ __('Accepted') }}</span>
                                        @elseif($proposal->status == 'Rejected')
                                            <span class="badge bg-danger">{{ __('Rejected') }}</span>
                                        @elseif($proposal->status == 'Close')
                                            <span class="badge bg-secondary">{{ __('Close') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="{{ route('proposal.show', $proposal->id) }}" class="btn btn-sm btn-outline-primary" title="{{ __('View') }}">
                                                <i class="ti ti-eye"></i>
                                            </a>
                                            @can('edit proposal')
                                                <a href="{{ route('proposal.edit', $proposal->id) }}" class="btn btn-sm btn-outline-secondary" title="{{ __('Edit') }}">
                                                    <i class="ti ti-edit"></i>
                                                </a>
                                            @endcan
                                            <a href="{{ route('proposal.pdf', $proposal->id) }}" class="btn btn-sm btn-outline-info" title="{{ __('Download') }}">
                                                <i class="ti ti-download"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center py-5">
                                        <div class="text-muted">
                                            <i class="ti ti-file-description" style="font-size: 3rem;"></i>
                                            <h5 class="mt-3">{{ __('No Quotes Found') }}</h5>
                                            <p>{{ __('Create your first quote to get started') }}</p>
                                            @can('create proposal')
                                                <a href="{{ route('proposal.create') }}" class="btn btn-primary">
                                                    <i class="ti ti-plus me-1"></i>{{ __('Create Quote') }}
                                                </a>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Setup View -->
<div id="setup-view" class="invoice-view" style="display: none;">
    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('Invoice Settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ route('taxes.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-receipt-tax me-2"></i>{{ __('Tax Settings') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="{{ route('product-category.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-category me-2"></i>{{ __('Product Categories') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="{{ route('product-unit.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-ruler me-2"></i>{{ __('Product Units') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="{{ route('settings') }}#payment-settings" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-credit-card me-2"></i>{{ __('Payment Methods') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('Template Settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ route('print.setting') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-printer me-2"></i>{{ __('Print Settings') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-template me-2"></i>{{ __('Invoice Templates') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-mail me-2"></i>{{ __('Email Templates') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                        <a href="{{ route('custom-field.index') }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="ti ti-forms me-2"></i>{{ __('Custom Fields') }}
                            </div>
                            <i class="ti ti-chevron-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Invoice view switching
    const invoiceViewButtons = document.querySelectorAll('[data-invoice-view]');
    const invoiceViews = document.querySelectorAll('.invoice-view');
    
    invoiceViewButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetView = this.getAttribute('data-invoice-view');
            
            // Remove active class from all buttons
            invoiceViewButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            // Hide all views
            invoiceViews.forEach(view => view.style.display = 'none');
            // Show target view
            const targetElement = document.getElementById(targetView + '-view');
            if (targetElement) {
                targetElement.style.display = 'block';
            }
        });
    });
});
</script>
