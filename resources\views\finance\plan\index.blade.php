@extends('layouts.admin')

@section('page-title')
    {{ __('Finance - Plan Management') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('finance.dashboard') }}">{{ __('Finance') }}</a></li>
    <li class="breadcrumb-item">{{ __('Plan') }}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-sm-12">
            <!-- Include Finance Horizontal Menu -->
            @include('layouts.finance_horizontal_menu')
            
            <!-- Plan Management Content -->
            <div class="row">
                <!-- Products Section -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="ti ti-box me-2"></i>{{ __('Products') }}
                            </h5>
                            @can('create product & service')
                                <a href="{{ route('productservice.create') }}" class="btn btn-sm btn-primary">
                                    <i class="ti ti-plus"></i>
                                </a>
                            @endcan
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <div class="theme-avtar bg-primary mx-auto mb-3" style="width: 60px; height: 60px;">
                                    <i class="ti ti-package" style="font-size: 1.5rem;"></i>
                                </div>
                                <h6 class="mb-2">{{ __('Manage Products & Services') }}</h6>
                                <p class="text-muted small">{{ __('Create and manage your products and services catalog') }}</p>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="{{ route('productservice.index') }}" class="btn btn-outline-primary">
                                    <i class="ti ti-list me-2"></i>{{ __('View All Products') }}
                                </a>
                                @can('create product & service')
                                    <a href="{{ route('productservice.create') }}" class="btn btn-primary">
                                        <i class="ti ti-plus me-2"></i>{{ __('Add New Product') }}
                                    </a>
                                @endcan
                            </div>

                            <!-- Quick Stats -->
                            <div class="mt-3 pt-3 border-top">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small class="text-muted">{{ __('Total Products') }}</small>
                                        <h6 class="mb-0">{{ \App\Models\ProductService::where('created_by', \Auth::user()->creatorId())->count() }}</h6>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">{{ __('Active') }}</small>
                                        <h6 class="mb-0">{{ \App\Models\ProductService::where('created_by', \Auth::user()->creatorId())->where('is_active', 1)->count() }}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Coupons Section -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="ti ti-ticket me-2"></i>{{ __('Coupons') }}
                            </h5>
                            @can('create coupon')
                                <a href="{{ route('coupons.create') }}" class="btn btn-sm btn-success">
                                    <i class="ti ti-plus"></i>
                                </a>
                            @endcan
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <div class="theme-avtar bg-success mx-auto mb-3" style="width: 60px; height: 60px;">
                                    <i class="ti ti-discount-2" style="font-size: 1.5rem;"></i>
                                </div>
                                <h6 class="mb-2">{{ __('Discount Coupons') }}</h6>
                                <p class="text-muted small">{{ __('Create and manage discount coupons for your customers') }}</p>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <a href="{{ route('coupons.index') }}" class="btn btn-outline-success">
                                    <i class="ti ti-list me-2"></i>{{ __('View All Coupons') }}
                                </a>
                                @can('create coupon')
                                    <a href="{{ route('coupons.create') }}" class="btn btn-success">
                                        <i class="ti ti-plus me-2"></i>{{ __('Create Coupon') }}
                                    </a>
                                @endcan
                            </div>

                            <!-- Quick Stats -->
                            <div class="mt-3 pt-3 border-top">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small class="text-muted">{{ __('Total Coupons') }}</small>
                                        <h6 class="mb-0">{{ \App\Models\Coupon::where('created_by', \Auth::user()->creatorId())->count() }}</h6>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">{{ __('Active') }}</small>
                                        <h6 class="mb-0">{{ \App\Models\Coupon::where('created_by', \Auth::user()->creatorId())->where('is_active', 1)->count() }}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Links Section -->
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="ti ti-link me-2"></i>{{ __('Links') }}
                            </h5>
                            <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#linksModal">
                                <i class="ti ti-plus"></i>
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <div class="theme-avtar bg-info mx-auto mb-3" style="width: 60px; height: 60px;">
                                    <i class="ti ti-external-link" style="font-size: 1.5rem;"></i>
                                </div>
                                <h6 class="mb-2">{{ __('Payment Links') }}</h6>
                                <p class="text-muted small">{{ __('Generate payment links for quick transactions') }}</p>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#linksModal">
                                    <i class="ti ti-list me-2"></i>{{ __('Manage Links') }}
                                </button>
                                <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#createLinkModal">
                                    <i class="ti ti-plus me-2"></i>{{ __('Create Link') }}
                                </button>
                            </div>

                            <!-- Quick Stats -->
                            <div class="mt-3 pt-3 border-top">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <small class="text-muted">{{ __('Total Links') }}</small>
                                        <h6 class="mb-0">0</h6>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted">{{ __('Active') }}</small>
                                        <h6 class="mb-0">0</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5>{{ __('Recent Plan Activities') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Type') }}</th>
                                            <th>{{ __('Name') }}</th>
                                            <th>{{ __('Action') }}</th>
                                            <th>{{ __('Date') }}</th>
                                            <th>{{ __('Status') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary">{{ __('Product') }}</span>
                                            </td>
                                            <td>{{ __('Sample Product') }}</td>
                                            <td>{{ __('Created') }}</td>
                                            <td>{{ now()->format('M d, Y') }}</td>
                                            <td>
                                                <span class="badge bg-success">{{ __('Active') }}</span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <span class="badge bg-success">{{ __('Coupon') }}</span>
                                            </td>
                                            <td>{{ __('Sample Coupon') }}</td>
                                            <td>{{ __('Created') }}</td>
                                            <td>{{ now()->subDay()->format('M d, Y') }}</td>
                                            <td>
                                                <span class="badge bg-success">{{ __('Active') }}</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Links Modal -->
    <div class="modal fade" id="linksModal" tabindex="-1" aria-labelledby="linksModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="linksModalLabel">{{ __('Payment Links') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">{{ __('Payment links feature will be available soon. This will allow you to create shareable payment links for quick transactions.') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Link Modal -->
    <div class="modal fade" id="createLinkModal" tabindex="-1" aria-labelledby="createLinkModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createLinkModalLabel">{{ __('Create Payment Link') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">{{ __('Create payment link feature will be available soon.') }}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                </div>
            </div>
        </div>
    </div>
@endsection
