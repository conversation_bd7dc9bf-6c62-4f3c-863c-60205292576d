<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            $table->decimal('payment_amount', 10, 2)->nullable()->after('description');
            $table->boolean('payment_required')->default(false)->after('payment_amount');
            $table->string('payment_currency', 3)->default('USD')->after('payment_required');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('calendar_events', function (Blueprint $table) {
            $table->dropColumn(['payment_amount', 'payment_required', 'payment_currency']);
        });
    }
}; 