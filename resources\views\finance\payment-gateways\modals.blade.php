<!-- Payment Gateway Configuration Modals -->

<!-- <PERSON><PERSON> -->
<div class="modal fade" id="stripeModal" tabindex="-1" aria-labelledby="stripeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stripeModalLabel">{{ __('Configure Stripe') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="stripePublishableKey" class="form-label">{{ __('Publishable Key') }}</label>
                        <input type="text" class="form-control" id="stripePublishableKey" placeholder="pk_test_...">
                    </div>
                    <div class="mb-3">
                        <label for="stripeSecretKey" class="form-label">{{ __('Secret Key') }}</label>
                        <input type="password" class="form-control" id="stripeSecretKey" placeholder="sk_test_...">
                    </div>
                    <div class="mb-3">
                        <label for="stripeWebhookSecret" class="form-label">{{ __('Webhook Secret') }}</label>
                        <input type="password" class="form-control" id="stripeWebhookSecret" placeholder="whsec_...">
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="stripeTestMode" checked>
                        <label class="form-check-label" for="stripeTestMode">{{ __('Test Mode') }}</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-primary">{{ __('Save Configuration') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- PayPal Modal -->
<div class="modal fade" id="paypalModal" tabindex="-1" aria-labelledby="paypalModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paypalModalLabel">{{ __('Configure PayPal') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="paypalClientId" class="form-label">{{ __('Client ID') }}</label>
                        <input type="text" class="form-control" id="paypalClientId" placeholder="PayPal Client ID">
                    </div>
                    <div class="mb-3">
                        <label for="paypalClientSecret" class="form-label">{{ __('Client Secret') }}</label>
                        <input type="password" class="form-control" id="paypalClientSecret" placeholder="PayPal Client Secret">
                    </div>
                    <div class="mb-3">
                        <label for="paypalMode" class="form-label">{{ __('Mode') }}</label>
                        <select class="form-select" id="paypalMode">
                            <option value="sandbox">{{ __('Sandbox') }}</option>
                            <option value="live">{{ __('Live') }}</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-info">{{ __('Save Configuration') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Razorpay Modal -->
<div class="modal fade" id="razorpayModal" tabindex="-1" aria-labelledby="razorpayModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="razorpayModalLabel">{{ __('Configure Razorpay') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="razorpayKeyId" class="form-label">{{ __('Key ID') }}</label>
                        <input type="text" class="form-control" id="razorpayKeyId" placeholder="rzp_test_...">
                    </div>
                    <div class="mb-3">
                        <label for="razorpayKeySecret" class="form-label">{{ __('Key Secret') }}</label>
                        <input type="password" class="form-control" id="razorpayKeySecret" placeholder="Razorpay Key Secret">
                    </div>
                    <div class="mb-3">
                        <label for="razorpayWebhookSecret" class="form-label">{{ __('Webhook Secret') }}</label>
                        <input type="password" class="form-control" id="razorpayWebhookSecret" placeholder="Webhook Secret">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-success">{{ __('Save Configuration') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Flutterwave Modal -->
<div class="modal fade" id="flutterwaveModal" tabindex="-1" aria-labelledby="flutterwaveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="flutterwaveModalLabel">{{ __('Configure Flutterwave') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="flutterwavePublicKey" class="form-label">{{ __('Public Key') }}</label>
                        <input type="text" class="form-control" id="flutterwavePublicKey" placeholder="FLWPUBK_TEST-...">
                    </div>
                    <div class="mb-3">
                        <label for="flutterwaveSecretKey" class="form-label">{{ __('Secret Key') }}</label>
                        <input type="password" class="form-control" id="flutterwaveSecretKey" placeholder="FLWSECK_TEST-...">
                    </div>
                    <div class="mb-3">
                        <label for="flutterwaveEncryptionKey" class="form-label">{{ __('Encryption Key') }}</label>
                        <input type="password" class="form-control" id="flutterwaveEncryptionKey" placeholder="Encryption Key">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-warning">{{ __('Save Configuration') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Paytm Modal -->
<div class="modal fade" id="paytmModal" tabindex="-1" aria-labelledby="paytmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paytmModalLabel">{{ __('Configure Paytm') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="paytmMerchantId" class="form-label">{{ __('Merchant ID') }}</label>
                        <input type="text" class="form-control" id="paytmMerchantId" placeholder="Merchant ID">
                    </div>
                    <div class="mb-3">
                        <label for="paytmMerchantKey" class="form-label">{{ __('Merchant Key') }}</label>
                        <input type="password" class="form-control" id="paytmMerchantKey" placeholder="Merchant Key">
                    </div>
                    <div class="mb-3">
                        <label for="paytmEnvironment" class="form-label">{{ __('Environment') }}</label>
                        <select class="form-select" id="paytmEnvironment">
                            <option value="staging">{{ __('Staging') }}</option>
                            <option value="production">{{ __('Production') }}</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-danger">{{ __('Save Configuration') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Bank Transfer Modal -->
<div class="modal fade" id="bankModal" tabindex="-1" aria-labelledby="bankModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bankModalLabel">{{ __('Configure Bank Transfer') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="bankName" class="form-label">{{ __('Bank Name') }}</label>
                        <input type="text" class="form-control" id="bankName" placeholder="Bank Name">
                    </div>
                    <div class="mb-3">
                        <label for="accountName" class="form-label">{{ __('Account Name') }}</label>
                        <input type="text" class="form-control" id="accountName" placeholder="Account Holder Name">
                    </div>
                    <div class="mb-3">
                        <label for="accountNumber" class="form-label">{{ __('Account Number') }}</label>
                        <input type="text" class="form-control" id="accountNumber" placeholder="Account Number">
                    </div>
                    <div class="mb-3">
                        <label for="routingNumber" class="form-label">{{ __('Routing Number / SWIFT Code') }}</label>
                        <input type="text" class="form-control" id="routingNumber" placeholder="Routing Number">
                    </div>
                    <div class="mb-3">
                        <label for="bankInstructions" class="form-label">{{ __('Payment Instructions') }}</label>
                        <textarea class="form-control" id="bankInstructions" rows="3" placeholder="Additional payment instructions for customers"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-primary">{{ __('Save Configuration') }}</button>
            </div>
        </div>
    </div>
</div>
