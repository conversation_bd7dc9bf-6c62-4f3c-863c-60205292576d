0<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UpdateBookingStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update all bookings with null status to 'scheduled'
        $updated = DB::table('bookings')
            ->whereNull('status')
            ->update(['status' => 'scheduled']);
            
        $this->command->info("Updated {$updated} bookings with null status to 'scheduled'");
        
        // Also update any empty string statuses
        $updatedEmpty = DB::table('bookings')
            ->where('status', '')
            ->update(['status' => 'scheduled']);
            
        $this->command->info("Updated {$updatedEmpty} bookings with empty status to 'scheduled'");
    }
}
