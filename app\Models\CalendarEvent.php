<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CalendarEvent extends Model
{
    protected $fillable = [
        'title',
        'start_date',
        'end_date',
        'duration',
        'booking_per_slot',
        'minimum_notice',
        'minimum_notice_value',
        'minimum_notice_unit',
        'description',
        'payment_amount',
        'payment_required',
        'payment_currency',
        'location',
        'locations_data',
        'custom_redirect_url',
        'meet_link',
        'physical_address',
        'require_name',
        'require_email',
        'require_phone',
        'weekly_availability',
        'custom_field',
        'custom_field_value',
        'custom_fields',
        'created_by',
        'assigned_staff_id',
        'date_override',
        'status',
        'date_range_type',
        'date_range_days',
        'date_range_start',
        'date_range_end',
    ];

    protected $casts = [
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'duration' => 'integer',
        'booking_per_slot' => 'integer',
        'minimum_notice' => 'integer',
        'require_name' => 'boolean',
        'require_email' => 'boolean',
        'require_phone' => 'boolean',
        'payment_required' => 'boolean',
        'payment_amount' => 'decimal:2',
        'custom_fields' => 'array',
        'custom_field_value' => 'array',
        'date_override' => 'array',
        'weekly_availability' => 'array',
        'locations_data' => 'array',
        'date_range_days' => 'integer',
        'date_range_start' => 'date',
        'date_range_end' => 'date'
    ];

    public function weeklyAvailability(): HasMany
    {
        return $this->hasMany(EventWeeklyAvailability::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function assignedStaff(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_staff_id');
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class, 'event_id');
    }

    public function appointmentBookings(): HasMany
    {
        return $this->hasMany(AppointmentBooking::class, 'event_id');
    }

    public function getFormattedDurationAttribute()
    {
        if (!$this->duration) return null;
        
        $hours = floor($this->duration / 60);
        $minutes = $this->duration % 60;
        
        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }


    
    

    

    public function getFormattedNoticeAttribute()
    {
        if (!$this->minimum_notice) return 'No notice required';


        $hours = floor($this->minimum_notice / 60);
        $days = floor($hours / 24);
        $weeks = floor($days / 7);

        if ($weeks > 0) return "{$weeks} week" . ($weeks > 1 ? 's' : '');
        if ($days > 0) return "{$days} day" . ($days > 1 ? 's' : '');
        if ($hours > 0) return "{$hours} hour" . ($hours > 1 ? 's' : '');
        return "{$this->minimum_notice} minute" . ($this->minimum_notice > 1 ? 's' : '');
    }

    public function getFormattedDateRangeAttribute()
    {
        switch ($this->date_range_type) {
            case 'calendar_days':
                return $this->date_range_days . ' calendar days into the future';
            case 'date_range':
                if ($this->date_range_start && $this->date_range_end) {
                    return $this->date_range_start->format('M j, Y') . ' - ' . $this->date_range_end->format('M j, Y');
                }
                return 'Date range not set';
            case 'indefinitely':
                return 'Indefinitely into the future';
            default:
                return 'No date range set';
        }
    }
}