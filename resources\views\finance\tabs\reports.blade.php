<!-- Reports Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-0">{{ __('Financial Reports') }}</h4>
        <p class="text-muted">{{ __('Comprehensive financial reporting and analytics') }}</p>
    </div>
</div>

<!-- Report Categories -->
<div class="row">
    <!-- Income Report -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="theme-avtar bg-success mx-auto mb-3" style="width: 60px; height: 60px;">
                    <i class="ti ti-trending-up" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-2">{{ __('Income Report') }}</h5>
                <p class="text-muted small mb-3">{{ __('Track revenue and income sources') }}</p>
                <a href="{{ route('report.income.summary') }}" class="btn btn-success">
                    <i class="ti ti-eye me-2"></i>{{ __('View Report') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Expense Report -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="theme-avtar bg-danger mx-auto mb-3" style="width: 60px; height: 60px;">
                    <i class="ti ti-trending-down" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-2">{{ __('Expense Report') }}</h5>
                <p class="text-muted small mb-3">{{ __('Monitor expenses and spending') }}</p>
                <a href="{{ route('report.expense.summary') }}" class="btn btn-danger">
                    <i class="ti ti-eye me-2"></i>{{ __('View Report') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Profit & Loss -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="theme-avtar bg-primary mx-auto mb-3" style="width: 60px; height: 60px;">
                    <i class="ti ti-chart-line" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-2">{{ __('Profit & Loss') }}</h5>
                <p class="text-muted small mb-3">{{ __('Comprehensive P&L statement') }}</p>
                <a href="{{ route('report.profit.loss') }}" class="btn btn-primary">
                    <i class="ti ti-eye me-2"></i>{{ __('View Report') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Overdue Report -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="theme-avtar bg-warning mx-auto mb-3" style="width: 60px; height: 60px;">
                    <i class="ti ti-clock-exclamation" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-2">{{ __('Overdue Report') }}</h5>
                <p class="text-muted small mb-3">{{ __('Track overdue invoices') }}</p>
                <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#overdueModal">
                    <i class="ti ti-eye me-2"></i>{{ __('View Report') }}
                </button>
            </div>
        </div>
    </div>

    <!-- GST Report -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="theme-avtar bg-info mx-auto mb-3" style="width: 60px; height: 60px;">
                    <i class="ti ti-receipt-tax" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-2">{{ __('GST Report') }}</h5>
                <p class="text-muted small mb-3">{{ __('Tax reports and calculations') }}</p>
                <button class="btn btn-info" data-bs-toggle="modal" data-bs-target="#gstModal">
                    <i class="ti ti-eye me-2"></i>{{ __('View Report') }}
                </button>
            </div>
        </div>
    </div>

    <!-- Invoice Report -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="theme-avtar bg-secondary mx-auto mb-3" style="width: 60px; height: 60px;">
                    <i class="ti ti-file-invoice" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-2">{{ __('Invoice Report') }}</h5>
                <p class="text-muted small mb-3">{{ __('Detailed invoice analytics') }}</p>
                <a href="{{ route('report.invoice.summary') }}" class="btn btn-secondary">
                    <i class="ti ti-eye me-2"></i>{{ __('View Report') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Customer Report -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="theme-avtar bg-dark mx-auto mb-3" style="width: 60px; height: 60px;">
                    <i class="ti ti-users" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-2">{{ __('Customer Report') }}</h5>
                <p class="text-muted small mb-3">{{ __('Customer analytics') }}</p>
                <button class="btn btn-dark" data-bs-toggle="modal" data-bs-target="#customerModal">
                    <i class="ti ti-eye me-2"></i>{{ __('View Report') }}
                </button>
            </div>
        </div>
    </div>

    <!-- Balance Sheet -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="theme-avtar bg-success mx-auto mb-3" style="width: 60px; height: 60px;">
                    <i class="ti ti-scale" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-2">{{ __('Balance Sheet') }}</h5>
                <p class="text-muted small mb-3">{{ __('Assets, liabilities, equity') }}</p>
                <a href="{{ route('report.balance.sheet') }}" class="btn btn-success">
                    <i class="ti ti-eye me-2"></i>{{ __('View Report') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Trial Balance -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-body text-center">
                <div class="theme-avtar bg-warning mx-auto mb-3" style="width: 60px; height: 60px;">
                    <i class="ti ti-balance" style="font-size: 1.5rem;"></i>
                </div>
                <h5 class="mb-2">{{ __('Trial Balance') }}</h5>
                <p class="text-muted small mb-3">{{ __('Account balances verification') }}</p>
                <a href="{{ route('trial.balance') }}" class="btn btn-warning">
                    <i class="ti ti-eye me-2"></i>{{ __('View Report') }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Quick Summary -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Financial Summary') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                <h4 class="text-success mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                                <small class="text-muted">{{ __('Total Income') }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                <h4 class="text-danger mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                                <small class="text-muted">{{ __('Total Expenses') }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                <h4 class="text-primary mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                                <small class="text-muted">{{ __('Net Profit') }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                <h4 class="text-warning mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                                <small class="text-muted">{{ __('Outstanding') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Report Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Report Actions') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#customReportModal">
                            <i class="ti ti-file-plus mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Custom Report') }}</span>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" onclick="window.print()">
                            <i class="ti ti-printer mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Print') }}</span>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#exportModal">
                            <i class="ti ti-download mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Export') }}</span>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#scheduleModal">
                            <i class="ti ti-calendar-event mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Schedule') }}</span>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#shareModal">
                            <i class="ti ti-share mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Share') }}</span>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('settings') }}" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-settings mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Settings') }}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include modals -->
@include('finance.reports.modals')
