<div class="finance-horizontal-menu-wrapper mb-4">
    <nav class="finance-horizontal-menu navbar navbar-expand-lg navbar-light bg-white shadow-sm rounded py-2 px-3" 
         role="navigation" aria-label="Finance Navigation">
        <div class="container-fluid p-0">
            <!-- Mobile Toggle Button -->
            <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" 
                    data-bs-target="#financeNavbar" aria-controls="financeNavbar" 
                    aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="financeNavbar">
                <ul class="navbar-nav flex-row flex-wrap w-100 justify-content-between justify-content-md-start gap-2 gap-md-3">
                    
                    <!-- Plan Menu -->
                    <li class="nav-item dropdown position-relative">
                        <a class="nav-link dropdown-toggle fw-semibold px-3 py-2 {{ 
                            Request::segment(1) == 'productservice' || Request::segment(1) == 'coupons' || Request::segment(1) == 'links' 
                            ? 'active text-primary bg-light' : 'text-dark' }}" 
                           href="#" id="planDropdown" role="button" data-bs-toggle="dropdown" 
                           aria-expanded="false" aria-haspopup="true">
                            <i class="ti ti-package me-1"></i> {{ __('Plan') }}
                        </a>
                        <ul class="dropdown-menu border-0 shadow-sm" aria-labelledby="planDropdown">
                            <li>
                                <a class="dropdown-item {{ Request::segment(1) == 'productservice' ? 'active fw-bold' : '' }}" 
                                   href="{{ route('productservice.index') }}">
                                    <i class="ti ti-box me-2"></i>{{ __('Products') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ Request::segment(1) == 'coupons' ? 'active fw-bold' : '' }}" 
                                   href="{{ route('coupons.index') }}">
                                    <i class="ti ti-ticket me-2"></i>{{ __('Coupons') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ Request::segment(1) == 'links' ? 'active fw-bold' : '' }}" 
                                   href="#!">
                                    <i class="ti ti-link me-2"></i>{{ __('Links') }}
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Sales Menu -->
                    <li class="nav-item dropdown position-relative">
                        <a class="nav-link dropdown-toggle fw-semibold px-3 py-2 {{ 
                            Request::segment(1) == 'subscription' || Request::segment(1) == 'installment' 
                            ? 'active text-primary bg-light' : 'text-dark' }}" 
                           href="#" id="salesDropdown" role="button" data-bs-toggle="dropdown" 
                           aria-expanded="false" aria-haspopup="true">
                            <i class="ti ti-shopping-cart me-1"></i> {{ __('Sales') }}
                        </a>
                        <ul class="dropdown-menu border-0 shadow-sm" aria-labelledby="salesDropdown">
                            <li>
                                <a class="dropdown-item {{ Request::segment(1) == 'subscription' ? 'active fw-bold' : '' }}" 
                                   href="#!">
                                    <i class="ti ti-repeat me-2"></i>{{ __('Subscription') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ Request::segment(1) == 'installment' ? 'active fw-bold' : '' }}" 
                                   href="#!">
                                    <i class="ti ti-calendar-time me-2"></i>{{ __('Installment') }}
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Invoices Menu -->
                    <li class="nav-item dropdown position-relative">
                        <a class="nav-link dropdown-toggle fw-semibold px-3 py-2 {{ 
                            Request::segment(1) == 'invoice' || Request::segment(1) == 'proposal' || Request::segment(1) == 'setup' 
                            ? 'active text-primary bg-light' : 'text-dark' }}" 
                           href="#" id="invoicesDropdown" role="button" data-bs-toggle="dropdown" 
                           aria-expanded="false" aria-haspopup="true">
                            <i class="ti ti-file-invoice me-1"></i> {{ __('Invoices') }}
                        </a>
                        <ul class="dropdown-menu border-0 shadow-sm" aria-labelledby="invoicesDropdown">
                            <li>
                                <a class="dropdown-item {{ Request::segment(1) == 'invoice' ? 'active fw-bold' : '' }}" 
                                   href="{{ route('invoice.index') }}">
                                    <i class="ti ti-file-text me-2"></i>{{ __('Invoices') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ Request::segment(1) == 'proposal' ? 'active fw-bold' : '' }}" 
                                   href="{{ route('proposal.index') }}">
                                    <i class="ti ti-file-description me-2"></i>{{ __('Quotes') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ Request::segment(1) == 'setup' ? 'active fw-bold' : '' }}" 
                                   href="#!">
                                    <i class="ti ti-settings me-2"></i>{{ __('Setup') }}
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Transactions Menu (no submenu) -->
                    <li class="nav-item">
                        <a class="nav-link fw-semibold px-3 py-2 {{ 
                            Request::segment(1) == 'transaction' ? 'active text-primary bg-light' : 'text-dark' }}" 
                           href="{{ route('transaction.index') }}">
                            <i class="ti ti-arrows-exchange me-1"></i> {{ __('Transactions') }}
                        </a>
                    </li>

                    <!-- Expenses Menu -->
                    <li class="nav-item dropdown position-relative">
                        <a class="nav-link dropdown-toggle fw-semibold px-3 py-2 {{ 
                            Request::segment(1) == 'expense' || Request::segment(1) == 'product-category' 
                            ? 'active text-primary bg-light' : 'text-dark' }}" 
                           href="#" id="expensesDropdown" role="button" data-bs-toggle="dropdown" 
                           aria-expanded="false" aria-haspopup="true">
                            <i class="ti ti-receipt me-1"></i> {{ __('Expenses') }}
                        </a>
                        <ul class="dropdown-menu border-0 shadow-sm" aria-labelledby="expensesDropdown">
                            <li>
                                <a class="dropdown-item {{ Request::segment(1) == 'expense' ? 'active fw-bold' : '' }}" 
                                   href="{{ route('expense.index') }}">
                                    <i class="ti ti-list me-2"></i>{{ __('Manage') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ Request::segment(1) == 'product-category' ? 'active fw-bold' : '' }}" 
                                   href="{{ route('product-category.index') }}">
                                    <i class="ti ti-category me-2"></i>{{ __('Categories') }}
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Reports Menu -->
                    <li class="nav-item dropdown position-relative">
                        <a class="nav-link dropdown-toggle fw-semibold px-3 py-2 {{ 
                            Request::segment(2) == 'income-summary' || Request::segment(2) == 'expense-summary' || 
                            Request::segment(2) == 'profit-loss' || Request::segment(2) == 'invoice-summary' 
                            ? 'active text-primary bg-light' : 'text-dark' }}" 
                           href="#" id="reportsDropdown" role="button" data-bs-toggle="dropdown" 
                           aria-expanded="false" aria-haspopup="true">
                            <i class="ti ti-chart-bar me-1"></i> {{ __('Reports') }}
                        </a>
                        <ul class="dropdown-menu border-0 shadow-sm" aria-labelledby="reportsDropdown">
                            <li>
                                <a class="dropdown-item {{ Request::segment(2) == 'income-summary' ? 'active fw-bold' : '' }}" 
                                   href="{{ route('report.income.summary') }}">
                                    <i class="ti ti-trending-up me-2"></i>{{ __('Income') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ Request::segment(2) == 'expense-summary' ? 'active fw-bold' : '' }}" 
                                   href="{{ route('report.expense.summary') }}">
                                    <i class="ti ti-trending-down me-2"></i>{{ __('Expense') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ Request::segment(2) == 'profit-loss' ? 'active fw-bold' : '' }}" 
                                   href="{{ route('report.profit.loss') }}">
                                    <i class="ti ti-chart-line me-2"></i>{{ __('Profit & Loss') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#!">
                                    <i class="ti ti-clock-exclamation me-2"></i>{{ __('Overdue') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#!">
                                    <i class="ti ti-receipt-tax me-2"></i>{{ __('GST') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item {{ Request::segment(2) == 'invoice-summary' ? 'active fw-bold' : '' }}" 
                                   href="{{ route('report.invoice.summary') }}">
                                    <i class="ti ti-file-invoice me-2"></i>{{ __('Invoices') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#!">
                                    <i class="ti ti-users me-2"></i>{{ __('Customers') }}
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Payment Gateways Menu (no submenu) -->
                    <li class="nav-item">
                        <a class="nav-link fw-semibold px-3 py-2 {{ 
                            Request::segment(1) == 'settings' && Request::get('tab') == 'payment' ? 'active text-primary bg-light' : 'text-dark' }}" 
                           href="{{ route('settings') }}#payment-settings">
                            <i class="ti ti-credit-card me-1"></i> {{ __('Payment Gateways') }}
                        </a>
                    </li>

                    <!-- Business Info Menu (no submenu) -->
                    <li class="nav-item">
                        <a class="nav-link fw-semibold px-3 py-2 {{ 
                            Request::segment(1) == 'settings' && Request::get('tab') == 'company' ? 'active text-primary bg-light' : 'text-dark' }}" 
                           href="{{ route('settings') }}#company-settings">
                            <i class="ti ti-building me-1"></i> {{ __('Business Info') }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
</div>

<style>
/* Finance Navigation Styles */
.finance-horizontal-menu-wrapper {
    position: relative;
}

.finance-horizontal-menu .nav-link {
    border-radius: 8px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.finance-horizontal-menu .nav-link:hover {
    background-color: #f8f9fa;
    color: #0d6efd !important;
}

.finance-horizontal-menu .nav-link.active {
    background-color: #e7f3ff;
    color: #0d6efd !important;
    font-weight: 600;
}

.finance-horizontal-menu .dropdown-menu {
    border-radius: 12px;
    padding: 8px 0;
    margin-top: 8px;
    min-width: 200px;
}

.finance-horizontal-menu .dropdown-item {
    padding: 8px 16px;
    border-radius: 6px;
    margin: 2px 8px;
    transition: all 0.2s ease;
}

.finance-horizontal-menu .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

.finance-horizontal-menu .dropdown-item.active {
    background-color: #e7f3ff;
    color: #0d6efd;
    font-weight: 600;
}

/* Mobile Responsive */
@media (max-width: 991.98px) {
    .finance-horizontal-menu .navbar-nav {
        flex-direction: column;
        width: 100%;
    }
    
    .finance-horizontal-menu .nav-item {
        width: 100%;
        margin-bottom: 4px;
    }
    
    .finance-horizontal-menu .dropdown-menu {
        position: static !important;
        transform: none !important;
        box-shadow: none;
        border: 1px solid #dee2e6;
        margin-left: 20px;
        margin-top: 4px;
    }
    
    .finance-horizontal-menu .dropdown-toggle::after {
        float: right;
        margin-top: 8px;
    }
}

/* Hover effects for desktop */
@media (min-width: 992px) {
    .finance-horizontal-menu .dropdown:hover .dropdown-menu {
        display: block;
        margin-top: 0;
    }
    
    .finance-horizontal-menu .dropdown-menu {
        display: none;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle dropdown toggles for mobile
    const dropdownToggles = document.querySelectorAll('.finance-horizontal-menu .dropdown-toggle');
    
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            if (window.innerWidth < 992) {
                e.preventDefault();
                const dropdownMenu = this.nextElementSibling;
                const isOpen = dropdownMenu.style.display === 'block';
                
                // Close all other dropdowns
                document.querySelectorAll('.finance-horizontal-menu .dropdown-menu').forEach(menu => {
                    menu.style.display = 'none';
                });
                
                // Toggle current dropdown
                dropdownMenu.style.display = isOpen ? 'none' : 'block';
                this.setAttribute('aria-expanded', !isOpen);
            }
        });
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.finance-horizontal-menu .dropdown')) {
            document.querySelectorAll('.finance-horizontal-menu .dropdown-menu').forEach(menu => {
                menu.style.display = 'none';
            });
            document.querySelectorAll('.finance-horizontal-menu .dropdown-toggle').forEach(toggle => {
                toggle.setAttribute('aria-expanded', 'false');
            });
        }
    });
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 992) {
            document.querySelectorAll('.finance-horizontal-menu .dropdown-menu').forEach(menu => {
                menu.style.display = '';
            });
        }
    });
});
</script>
