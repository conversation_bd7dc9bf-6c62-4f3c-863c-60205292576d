<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // For MySQL, we need to alter the enum column to include 'phone'
        DB::statement("ALTER TABLE calendar_events MODIFY COLUMN location ENUM('in_person', 'zoom', 'skype', 'meet', 'phone', 'others') NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove 'phone' from the enum (be careful - this will fail if any records have 'phone' as location)
        DB::statement("ALTER TABLE calendar_events MODIFY COLUMN location ENUM('in_person', 'zoom', 'skype', 'meet', 'others') NULL");
    }
};
