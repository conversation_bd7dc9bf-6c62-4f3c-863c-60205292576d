{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "anandsiddharth/laravel-paytm-wallet": "^2.0", "anhskohbo/no-captcha": "^3.6", "arkitecht/laravel-twilio": "^1.6", "doctrine/dbal": "^4.1", "fedapay/fedapay-php": "^0.4.2", "guzzlehttp/guzzle": "^7.9", "iyzico/iyzipay-php": "^2.0", "kkomelin/laravel-translatable-string-exporter": "^1.22", "konekt/html": "^6.5", "lab404/laravel-impersonate": "^1.7", "lahirulhr/laravel-payhere": "^1.0", "laravel/framework": "^11.9", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "laravel/ui": "^4.5", "league/flysystem-aws-s3-v3": "^3.28", "maatwebsite/excel": "^3.1", "mashape/unirest-php": "^3.0", "mercadopago/dx-php": "^2.5", "midtrans/midtrans-php": "^2.5", "milon/barcode": "^11.0", "mollie/mollie-api-php": "^2.71", "munafio/chatify": "^1.5", "nwidart/laravel-modules": "^11.1", "obydul/laraskrill": "^1.2", "orhanerday/open-ai": "^5.2", "paymentwall/paymentwall-php": "^2.2", "psr/simple-cache": "^3.0", "rachidlaasri/laravel-installer": "^4.1", "razorpay/razorpay": "^2.8", "spatie/browsershot": "^4.3", "spatie/laravel-google-calendar": "^3.8", "spatie/laravel-permission": "^6.9", "srmklive/paypal": "^3.0", "stripe/stripe-php": "^15.7", "twilio/sdk": "^7.16", "whichbrowser/parser": "^2.1", "yoomoney/yookassa-sdk-php": "^3.5"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.9", "fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "Mo<PERSON>les/", "Easebuzz\\": "app/Libraries/Easebuzz/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}