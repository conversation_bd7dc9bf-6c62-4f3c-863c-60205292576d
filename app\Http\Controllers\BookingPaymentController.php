<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\CalendarEvent;
use App\Models\Utility;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class BookingPaymentController extends Controller
{
    public $secret_key;
    public $public_key;
    public $is_enabled;
    public $currency;
    public $payment_method;

    public function paymentConfig($method = 'razorpay', $creator_id = null)
    {
        Log::info('paymentConfig called with method: ' . $method . ', creator_id: ' . $creator_id);
        
        // If creator_id is not provided and user is authenticated, use user's creator ID
        if (!$creator_id && Auth::check()) {
            $creator_id = Auth::user()->creatorId();
        }
        
        // If still no creator_id, we need to get it from the event
        if (!$creator_id) {
            Log::warning('No creator_id provided, returning empty config');
            return $this;
        }
        
        $payment_setting = Utility::getCompanyPaymentSetting($creator_id);
        $setting = Utility::settingsById($creator_id);

        $this->payment_method = $method;
        
        // Set currency based on payment method and settings
        if ($method === 'razorpay') {
            // Razorpay only supports INR
            $this->currency = 'INR';
        } else {
            // Use booking payment currency if set, otherwise fallback to site currency
            $this->currency = isset($payment_setting['booking_payment_currency']) ? $payment_setting['booking_payment_currency'] : (isset($setting['site_currency']) ? $setting['site_currency'] : 'USD');
        }

        switch ($method) {
            case 'stripe':
                $this->public_key = isset($payment_setting['booking_stripe_key']) ? $payment_setting['booking_stripe_key'] : '';
                $this->secret_key = isset($payment_setting['booking_stripe_secret']) ? $payment_setting['booking_stripe_secret'] : '';
                $this->is_enabled = isset($payment_setting['is_booking_stripe_enabled']) ? $payment_setting['is_booking_stripe_enabled'] : 'off';
                break;
            case 'paypal':
                $this->public_key = isset($payment_setting['booking_paypal_client_id']) ? $payment_setting['booking_paypal_client_id'] : '';
                $this->secret_key = isset($payment_setting['booking_paypal_secret_key']) ? $payment_setting['booking_paypal_secret_key'] : '';
                $this->is_enabled = isset($payment_setting['is_booking_paypal_enabled']) ? $payment_setting['is_booking_paypal_enabled'] : 'off';
                break;
            case 'razorpay':
                $this->public_key = isset($payment_setting['booking_razorpay_public_key']) ? $payment_setting['booking_razorpay_public_key'] : '';
                $this->secret_key = isset($payment_setting['booking_razorpay_secret_key']) ? $payment_setting['booking_razorpay_secret_key'] : '';
                $this->is_enabled = isset($payment_setting['is_booking_razorpay_enabled']) ? $payment_setting['is_booking_razorpay_enabled'] : 'off';
                break;
            case 'paystack':
                $this->public_key = isset($payment_setting['booking_paystack_public_key']) ? $payment_setting['booking_paystack_public_key'] : '';
                $this->secret_key = isset($payment_setting['booking_paystack_secret_key']) ? $payment_setting['booking_paystack_secret_key'] : '';
                $this->is_enabled = isset($payment_setting['is_booking_paystack_enabled']) ? $payment_setting['is_booking_paystack_enabled'] : 'off';
                break;
            case 'flutterwave':
                $this->public_key = isset($payment_setting['booking_flutterwave_public_key']) ? $payment_setting['booking_flutterwave_public_key'] : '';
                $this->secret_key = isset($payment_setting['booking_flutterwave_secret_key']) ? $payment_setting['booking_flutterwave_secret_key'] : '';
                $this->is_enabled = isset($payment_setting['is_booking_flutterwave_enabled']) ? $payment_setting['is_booking_flutterwave_enabled'] : 'off';
                break;
            default:
                $this->public_key = '';
                $this->secret_key = '';
                $this->is_enabled = 'off';
        }

        return $this;
    }

    /**
     * Get available payment methods for booking
     */
    public function getAvailablePaymentMethods(Request $request = null)
    {
        // Get creator_id from request if provided, otherwise use authenticated user
        $creator_id = null;
        if ($request && $request->has('event_id')) {
            $event = CalendarEvent::find($request->event_id);
            if ($event) {
                $creator_id = $event->created_by;
            }
        }
        
        if (!$creator_id && Auth::check()) {
            $creator_id = Auth::user()->creatorId();
        }
        
        if (!$creator_id) {
            return [];
        }
        
        $payment_setting = Utility::getCompanyPaymentSetting($creator_id);
        $available_methods = [];

        // Check which payment methods are enabled
        if (isset($payment_setting['is_booking_stripe_enabled']) && $payment_setting['is_booking_stripe_enabled'] == 'on') {
            $available_methods[] = 'stripe';
        }
        if (isset($payment_setting['is_booking_paypal_enabled']) && $payment_setting['is_booking_paypal_enabled'] == 'on') {
            $available_methods[] = 'paypal';
        }
        if (isset($payment_setting['is_booking_razorpay_enabled']) && $payment_setting['is_booking_razorpay_enabled'] == 'on') {
            $available_methods[] = 'razorpay';
        }
        if (isset($payment_setting['is_booking_paystack_enabled']) && $payment_setting['is_booking_paystack_enabled'] == 'on') {
            $available_methods[] = 'paystack';
        }
        if (isset($payment_setting['is_booking_flutterwave_enabled']) && $payment_setting['is_booking_flutterwave_enabled'] == 'on') {
            $available_methods[] = 'flutterwave';
        }
        if (isset($payment_setting['is_booking_manual_enabled']) && $payment_setting['is_booking_manual_enabled'] == 'on') {
            $available_methods[] = 'manual';
        }
        if (isset($payment_setting['is_booking_bank_transfer_enabled']) && $payment_setting['is_booking_bank_transfer_enabled'] == 'on') {
            $available_methods[] = 'bank_transfer';
        }

        return $available_methods;
    }

    /**
     * Initialize payment for booking
     */
    public function initializePayment(Request $request)
    {
        try {
            // Log the incoming request data for debugging
            Log::info('Payment initialization request data:', $request->all());
            
            $request->validate([
                'booking_id' => 'required|exists:bookings,id',
                'amount' => 'required|numeric|min:0',
                'payment_method' => 'nullable|string'
            ]);

            $booking = Booking::with('event')->findOrFail($request->booking_id);
            Log::info('Booking found - ID: ' . $booking->id . ', Event ID: ' . $booking->event_id . ', Event Creator: ' . $booking->event->created_by);
            
            // For authenticated users, check if booking belongs to current user's events
            if (Auth::check() && $booking->event->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to booking'
                ], 403);
            }

            // Check if payment is required for this event
            if (!$booking->event->payment_required) {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment not required for this event'
                ], 400);
            }

            $payment_method = $request->payment_method ?? 'razorpay';
            Log::info('Payment method: ' . $payment_method . ', Creator ID: ' . $booking->event->created_by);
            $payment = $this->paymentConfig($payment_method, $booking->event->created_by);

            Log::info('Payment config - method: ' . $payment_method . ', enabled: ' . $payment->is_enabled . ', public_key: ' . $payment->public_key);
            
            if ($payment->is_enabled !== 'on') {
                return response()->json([
                    'success' => false,
                    'message' => ucfirst($payment_method) . ' payment is not enabled'
                ], 400);
            }

            // Update booking with payment amount and method
            $booking->update([
                'payment_amount' => $request->amount,
                'payment_status' => 'pending',
                'payment_method' => $payment_method
            ]);

            // Return payment initialization data based on method
            $res_data = [
                'email' => $booking->email,
                'total_price' => $request->amount,
                'currency' => $payment->currency,
                'booking_id' => $booking->id,
                'payment_method' => $payment_method,
                'flag' => 1
            ];

            // Add method-specific data
            switch ($payment_method) {
                case 'stripe':
                    $res_data['stripe_key'] = $payment->public_key;
                    break;
                case 'razorpay':
                    $res_data['razorpay_key'] = $payment->public_key;
                    break;
                case 'paystack':
                    $res_data['paystack_key'] = $payment->public_key;
                    break;
                case 'flutterwave':
                    $res_data['flutterwave_key'] = $payment->public_key;
                    break;
            }

            Log::info('Payment initialization response:', $res_data);
            return response()->json($res_data);

        } catch (\Exception $e) {
            Log::error('Booking payment initialization failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Payment initialization failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle payment callback for different gateways
     */
    public function handlePaymentCallback(Request $request, $payment_id, $booking_id)
    {
        try {
            Log::info('Payment callback received - Payment ID: ' . $payment_id . ', Booking ID: ' . $booking_id . ', Amount: ' . $request->query('amount'));
            
            $booking = Booking::with('event')->findOrFail($booking_id);
            
            // For authenticated users, check if booking belongs to current user's events
            if (Auth::check() && $booking->event->created_by !== Auth::id()) {
                return redirect()->back()->with('error', 'Unauthorized access to booking');
            }

            $payment_method = $booking->payment_method ?? 'razorpay';
            $payment = $this->paymentConfig($payment_method, $booking->event->created_by);
            $amount = $request->query('amount');

            // Handle payment verification based on method
            $payment_successful = false;
            $transaction_details = [];

            switch ($payment_method) {
                case 'razorpay':
                    $payment_successful = $this->verifyRazorpayPayment($payment_id, $amount, $payment);
                    Log::info('Razorpay payment verification result: ' . ($payment_successful ? 'SUCCESS' : 'FAILED'));
                    $transaction_details = [
                        'razorpay_payment_id' => $payment_id,
                        'amount' => $amount,
                        'currency' => $payment->currency,
                        'method' => 'razorpay'
                    ];
                    break;
                case 'stripe':
                    $payment_successful = $this->verifyStripePayment($payment_id, $amount, $payment);
                    $transaction_details = [
                        'stripe_payment_intent_id' => $payment_id,
                        'amount' => $amount,
                        'currency' => $payment->currency,
                        'method' => 'stripe'
                    ];
                    break;
                case 'paypal':
                    $payment_successful = $this->verifyPayPalPayment($payment_id, $amount, $payment);
                    $transaction_details = [
                        'paypal_payment_id' => $payment_id,
                        'amount' => $amount,
                        'currency' => $payment->currency,
                        'method' => 'paypal'
                    ];
                    break;
                case 'paystack':
                    $payment_successful = $this->verifyPaystackPayment($payment_id, $amount, $payment);
                    $transaction_details = [
                        'paystack_reference' => $payment_id,
                        'amount' => $amount,
                        'currency' => $payment->currency,
                        'method' => 'paystack'
                    ];
                    break;
                case 'flutterwave':
                    $payment_successful = $this->verifyFlutterwavePayment($payment_id, $amount, $payment);
                    $transaction_details = [
                        'flutterwave_transaction_id' => $payment_id,
                        'amount' => $amount,
                        'currency' => $payment->currency,
                        'method' => 'flutterwave'
                    ];
                    break;
                default:
                    return redirect()->route('bookings.show', $booking->id)
                        ->with('error', 'Unsupported payment method.');
            }

            if ($payment_successful) {
                // Payment successful
                $booking->update([
                    'payment_status' => 'paid',
                    'payment_transaction_id' => $payment_id,
                    'payment_date' => now(),
                    'payment_details' => array_merge($transaction_details, [
                        'status' => 'success',
                        'captured_at' => now()->toISOString()
                    ])
                ]);

                // For public users, redirect to booking confirmation page
                if (!Auth::check()) {
                    return redirect()->route('booking.confirmation', $booking->id)
                        ->with('success', 'Payment completed successfully! Your booking has been confirmed.');
                }

                return redirect()->route('bookings.show', $booking->id)
                    ->with('success', 'Payment completed successfully!');
            } else {
                // Payment failed
                $booking->update([
                    'payment_status' => 'failed',
                    'payment_transaction_id' => $payment_id,
                    'payment_details' => array_merge($transaction_details, [
                        'status' => 'failed',
                        'error' => 'Payment verification failed'
                    ])
                ]);

                // For public users, redirect to a failure page or back to the event
                if (!Auth::check()) {
                    return redirect()->back()->with('error', 'Payment verification failed. Please try again.');
                }

                return redirect()->route('bookings.show', $booking->id)
                    ->with('error', 'Payment failed. Please try again.');
            }

        } catch (\Exception $e) {
            Log::error('Booking payment callback failed: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Payment processing failed: ' . $e->getMessage());
        }
    }

    /**
     * Verify Razorpay payment
     */
    private function verifyRazorpayPayment($payment_id, $amount, $payment)
    {
        try {
            Log::info('Verifying Razorpay payment - ID: ' . $payment_id . ', Amount: ' . $amount . ', Currency: ' . $payment->currency);
            
            $api = new \Razorpay\Api\Api($payment->public_key, $payment->secret_key);
            $payment_data = $api->payment->fetch($payment_id);
            
            Log::info('Razorpay payment data:', [
                'payment_id' => $payment_id,
                'status' => $payment_data->status,
                'amount' => $payment_data->amount,
                'currency' => $payment_data->currency,
                'expected_amount' => $amount * 100 // Convert to paise
            ]);
            
            // Check if payment is authorized/captured and amount matches
            $expected_amount_paise = $amount * 100; // Convert to paise
            $is_valid = ($payment_data->status === 'captured' || $payment_data->status === 'authorized') && 
                       $payment_data->amount == $expected_amount_paise &&
                       $payment_data->currency === $payment->currency;
            
            Log::info('Razorpay payment verification result: ' . ($is_valid ? 'SUCCESS' : 'FAILED'));
            
            return $is_valid;
        } catch (\Exception $e) {
            Log::error('Razorpay payment verification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify Stripe payment
     */
    private function verifyStripePayment($payment_intent_id, $amount, $payment)
    {
        try {
            \Stripe\Stripe::setApiKey($payment->secret_key);
            $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);
            return $payment_intent->status === 'succeeded';
        } catch (\Exception $e) {
            Log::error('Stripe payment verification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify PayPal payment
     */
    private function verifyPayPalPayment($payment_id, $amount, $payment)
    {
        try {
            // PayPal verification logic would go here
            // This is a simplified version - you'd need to implement proper PayPal verification
            return true;
        } catch (\Exception $e) {
            Log::error('PayPal payment verification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify Paystack payment
     */
    private function verifyPaystackPayment($reference, $amount, $payment)
    {
        try {
            // Paystack verification logic would go here
            // This is a simplified version - you'd need to implement proper Paystack verification
            return true;
        } catch (\Exception $e) {
            Log::error('Paystack payment verification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Verify Flutterwave payment
     */
    private function verifyFlutterwavePayment($transaction_id, $amount, $payment)
    {
        try {
            // Flutterwave verification logic would go here
            // This is a simplified version - you'd need to implement proper Flutterwave verification
            return true;
        } catch (\Exception $e) {
            Log::error('Flutterwave payment verification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get payment status for a booking
     */
    public function getPaymentStatus($booking_id)
    {
        try {
            $booking = Booking::with('event')->findOrFail($booking_id);
            
            // For authenticated users, check if booking belongs to current user's events
            if (Auth::check() && $booking->event->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to booking'
                ], 403);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'payment_status' => $booking->payment_status,
                    'payment_amount' => $booking->payment_amount,
                    'payment_method' => $booking->payment_method,
                    'payment_date' => $booking->payment_date,
                    'payment_required' => $booking->event->payment_required
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Refund payment for a booking
     */
    public function refundPayment(Request $request, $booking_id)
    {
        try {
            $request->validate([
                'refund_amount' => 'required|numeric|min:0'
            ]);

            $booking = Booking::with('event')->findOrFail($booking_id);
            
            // For authenticated users, check if booking belongs to current user's events
            if (Auth::check() && $booking->event->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to booking'
                ], 403);
            }

            if ($booking->payment_status !== 'paid') {
                return response()->json([
                    'success' => false,
                    'message' => 'Booking is not paid'
                ], 400);
            }

            if ($request->refund_amount > $booking->payment_amount) {
                return response()->json([
                    'success' => false,
                    'message' => 'Refund amount cannot exceed payment amount'
                ], 400);
            }

            $payment_method = $booking->payment_method ?? 'razorpay';
            $payment = $this->paymentConfig($payment_method, $booking->event->created_by);

            if ($payment->is_enabled !== 'on') {
                return response()->json([
                    'success' => false,
                    'message' => ucfirst($payment_method) . ' payment is not enabled'
                ], 400);
            }

            // Process refund based on payment method
            $refund_successful = false;
            $refund_id = null;

            switch ($payment_method) {
                case 'razorpay':
                    $refund_successful = $this->processRazorpayRefund($booking, $request->refund_amount, $payment);
                    break;
                case 'stripe':
                    $refund_successful = $this->processStripeRefund($booking, $request->refund_amount, $payment);
                    break;
                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Refund not supported for this payment method'
                    ], 400);
            }

            if ($refund_successful) {
                $booking->update([
                    'payment_status' => 'refunded',
                    'payment_details' => array_merge($booking->payment_details ?? [], [
                        'refund_amount' => $request->refund_amount,
                        'refunded_at' => now()->toISOString()
                    ])
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Payment refunded successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Refund failed'
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Booking refund failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Refund processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process Razorpay refund
     */
    private function processRazorpayRefund($booking, $refund_amount, $payment)
    {
        try {
            $api = new \Razorpay\Api\Api($payment->public_key, $payment->secret_key);
            $refund = $api->refund->create([
                'payment_id' => $booking->payment_transaction_id,
                'amount' => $refund_amount * 100, // Convert to paise
                'speed' => 'normal'
            ]);
            return true;
        } catch (\Exception $e) {
            Log::error('Razorpay refund failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Process Stripe refund
     */
    private function processStripeRefund($booking, $refund_amount, $payment)
    {
        try {
            \Stripe\Stripe::setApiKey($payment->secret_key);
            $refund = \Stripe\Refund::create([
                'payment_intent' => $booking->payment_transaction_id,
                'amount' => $refund_amount * 100 // Convert to cents
            ]);
            return true;
        } catch (\Exception $e) {
            Log::error('Stripe refund failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Test payment method
     */
    public function testPayment($booking_id)
    {
        try {
            $booking = Booking::with('event')->findOrFail($booking_id);
            
            // Check if booking belongs to current user's events
            if ($booking->event->created_by !== Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to booking'
                ], 403);
            }

            $available_methods = $this->getAvailablePaymentMethods();

            return response()->json([
                'success' => true,
                'data' => [
                    'available_methods' => $available_methods,
                    'booking' => $booking,
                    'event' => $booking->event
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get payment methods: ' . $e->getMessage()
            ], 500);
        }
    }
} 