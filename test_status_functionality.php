<?php

require_once 'vendor/autoload.php';

use App\Models\CalendarEvent;
use App\Models\User;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

try {
    echo "Testing Calendar Event Status Functionality\n";
    echo "==========================================\n\n";

    // Create a test user
    $user = User::first();
    if (!$user) {
        echo "Creating test user...\n";
        $user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'type' => 'company'
        ]);
        echo "✓ Test user created\n";
    }

    echo "Using user: {$user->name} (ID: {$user->id})\n\n";

    // Test 1: Create event with active status
    echo "Test 1: Creating event with active status...\n";
    $activeEvent = CalendarEvent::create([
        'title' => 'Test Active Event',
        'duration' => 60,
        'booking_per_slot' => 1,
        'minimum_notice' => 0,
        'description' => 'Test active event',
        'status' => 'active',
        'created_by' => $user->id,
        'start_date' => now(),
        'end_date' => now()->addYear()
    ]);
    echo "✓ Active event created with ID: {$activeEvent->id}, Status: {$activeEvent->status}\n\n";

    // Test 2: Create event with inactive status
    echo "Test 2: Creating event with inactive status...\n";
    $inactiveEvent = CalendarEvent::create([
        'title' => 'Test Inactive Event',
        'duration' => 60,
        'booking_per_slot' => 1,
        'minimum_notice' => 0,
        'description' => 'Test inactive event',
        'status' => 'inactive',
        'created_by' => $user->id,
        'start_date' => now(),
        'end_date' => now()->addYear()
    ]);
    echo "✓ Inactive event created with ID: {$inactiveEvent->id}, Status: {$inactiveEvent->status}\n\n";

    // Test 3: Create event without status (should default to active)
    echo "Test 3: Creating event without status (should default to active)...\n";
    $defaultEvent = CalendarEvent::create([
        'title' => 'Test Default Event',
        'duration' => 60,
        'booking_per_slot' => 1,
        'minimum_notice' => 0,
        'description' => 'Test default event',
        'created_by' => $user->id,
        'start_date' => now(),
        'end_date' => now()->addYear()
    ]);
    echo "✓ Default event created with ID: {$defaultEvent->id}, Status: {$defaultEvent->status}\n\n";

    // Test 4: Update event status
    echo "Test 4: Updating event status from active to inactive...\n";
    $activeEvent->update(['status' => 'inactive']);
    $activeEvent->refresh();
    echo "✓ Event {$activeEvent->id} status updated to: {$activeEvent->status}\n\n";

    // Test 5: Retrieve all events and show their statuses
    echo "Test 5: Retrieving all test events and their statuses...\n";
    $events = CalendarEvent::whereIn('title', [
        'Test Active Event',
        'Test Inactive Event', 
        'Test Default Event'
    ])->get();

    foreach ($events as $event) {
        echo "- {$event->title}: {$event->status}\n";
    }
    echo "\n";

    // Test 6: Test fillable array includes status
    echo "Test 6: Testing fillable array includes status...\n";
    $fillable = (new CalendarEvent())->getFillable();
    if (in_array('status', $fillable)) {
        echo "✓ Status field is in fillable array\n";
    } else {
        echo "✗ Status field is NOT in fillable array\n";
    }
    echo "\n";

    // Clean up test data
    echo "Cleaning up test data...\n";
    CalendarEvent::whereIn('title', [
        'Test Active Event',
        'Test Inactive Event', 
        'Test Default Event'
    ])->delete();
    echo "✓ Test data cleaned up\n\n";

    echo "All tests completed successfully! ✓\n";
    echo "The status functionality is working correctly.\n";

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    exit(1);
}
