/* Finance Module Custom Styles */

/* Finance Navigation Enhancements */
.finance-horizontal-menu-wrapper {
    position: relative;
    z-index: 1000;
}

.finance-horizontal-menu {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
}

.finance-horizontal-menu .nav-link {
    position: relative;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    letter-spacing: 0.025em;
}

.finance-horizontal-menu .nav-link:hover {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    color: #1976d2 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
}

.finance-horizontal-menu .nav-link.active {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    color: #ffffff !important;
    box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
}

.finance-horizontal-menu .nav-link.active::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    background: #ffffff;
    border-radius: 50%;
}

/* Dropdown Enhancements */
.finance-horizontal-menu .dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.12);
    padding: 12px 0;
    margin-top: 12px;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.finance-horizontal-menu .dropdown-item {
    padding: 10px 20px;
    border-radius: 8px;
    margin: 2px 12px;
    transition: all 0.2s ease;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.finance-horizontal-menu .dropdown-item:hover {
    background: linear-gradient(135deg, #f3e5f5 0%, #e8f5e8 100%);
    color: #1976d2;
    transform: translateX(4px);
}

.finance-horizontal-menu .dropdown-item.active {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    color: #ffffff;
    font-weight: 600;
}

.finance-horizontal-menu .dropdown-item i {
    width: 20px;
    text-align: center;
}

/* Mobile Responsive Enhancements */
@media (max-width: 991.98px) {
    .finance-horizontal-menu .navbar-nav {
        background: #ffffff;
        border-radius: 12px;
        padding: 16px;
        margin-top: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .finance-horizontal-menu .nav-item {
        margin-bottom: 8px;
    }
    
    .finance-horizontal-menu .dropdown-menu {
        position: static !important;
        transform: none !important;
        box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.05);
        background: #f8f9fa;
        margin-left: 16px;
        margin-top: 8px;
        border-radius: 8px;
    }
}

/* Finance Dashboard Cards */
.finance-card {
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.finance-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.finance-card .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #e9ecef;
    padding: 20px;
}

.finance-card .card-body {
    padding: 24px;
}

/* Theme Avatar Enhancements */
.theme-avtar {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.theme-avtar:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Button Enhancements */
.btn-finance {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 20px;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-finance::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-finance:hover::before {
    left: 100%;
}

.btn-finance:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Quick Actions Grid */
.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-top: 20px;
}

.quick-action-item {
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #495057;
}

.quick-action-item:hover {
    border-color: #1976d2;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    color: #1976d2;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(25, 118, 210, 0.15);
}

/* Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

/* Loading States */
.finance-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1976d2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .finance-horizontal-menu .nav-link {
        padding: 12px 16px;
        font-size: 0.9rem;
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 12px;
    }
    
    .quick-action-item {
        padding: 16px 12px;
    }
    
    .stat-card {
        padding: 16px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .finance-horizontal-menu {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        border-color: #4a5568;
    }
    
    .finance-horizontal-menu .nav-link {
        color: #e2e8f0;
    }
    
    .finance-horizontal-menu .dropdown-menu {
        background: rgba(45, 55, 72, 0.95);
        border-color: #4a5568;
    }
    
    .finance-card {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        border-color: #4a5568;
        color: #e2e8f0;
    }
}
