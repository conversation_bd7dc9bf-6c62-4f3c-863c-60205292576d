<?php

namespace Tests\Feature;

use App\Models\CalendarEvent;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CalendarEventStatusTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'type' => 'company'
        ]);
    }

    /** @test */
    public function it_can_create_calendar_event_with_active_status()
    {
        $this->actingAs($this->user);

        $eventData = [
            'title' => 'Test Event',
            'duration' => 60,
            'booking_per_slot' => 1,
            'minimum_notice' => 0,
            'description' => 'Test description',
            'status' => 'active'
        ];

        $response = $this->postJson(route('calendar-events.store'), $eventData);

        $response->assertStatus(200)
                ->assertJson(['success' => true]);

        $this->assertDatabaseHas('calendar_events', [
            'title' => 'Test Event',
            'status' => 'active',
            'created_by' => $this->user->id
        ]);
    }

    /** @test */
    public function it_can_create_calendar_event_with_inactive_status()
    {
        $this->actingAs($this->user);

        $eventData = [
            'title' => 'Test Inactive Event',
            'duration' => 60,
            'booking_per_slot' => 1,
            'minimum_notice' => 0,
            'description' => 'Test description',
            'status' => 'inactive'
        ];

        $response = $this->postJson(route('calendar-events.store'), $eventData);

        $response->assertStatus(200)
                ->assertJson(['success' => true]);

        $this->assertDatabaseHas('calendar_events', [
            'title' => 'Test Inactive Event',
            'status' => 'inactive',
            'created_by' => $this->user->id
        ]);
    }

    /** @test */
    public function it_defaults_to_active_status_when_not_provided()
    {
        $this->actingAs($this->user);

        $eventData = [
            'title' => 'Test Event Without Status',
            'duration' => 60,
            'booking_per_slot' => 1,
            'minimum_notice' => 0,
            'description' => 'Test description'
            // No status provided
        ];

        $response = $this->postJson(route('calendar-events.store'), $eventData);

        $response->assertStatus(200)
                ->assertJson(['success' => true]);

        $this->assertDatabaseHas('calendar_events', [
            'title' => 'Test Event Without Status',
            'status' => 'active', // Should default to active
            'created_by' => $this->user->id
        ]);
    }

    /** @test */
    public function it_can_update_calendar_event_status()
    {
        $this->actingAs($this->user);

        // Create an event first
        $event = CalendarEvent::create([
            'title' => 'Test Event',
            'duration' => 60,
            'booking_per_slot' => 1,
            'minimum_notice' => 0,
            'description' => 'Test description',
            'status' => 'active',
            'created_by' => $this->user->id,
            'start_date' => now(),
            'end_date' => now()->addYear()
        ]);

        // Update the event status
        $updateData = [
            'title' => 'Test Event',
            'duration' => 60,
            'booking_per_slot' => 1,
            'minimum_notice' => 0,
            'description' => 'Test description',
            'status' => 'inactive'
        ];

        $response = $this->putJson(route('calendar-events.update', $event->id), $updateData);

        $response->assertStatus(200)
                ->assertJson(['success' => true]);

        $this->assertDatabaseHas('calendar_events', [
            'id' => $event->id,
            'status' => 'inactive'
        ]);
    }

    /** @test */
    public function it_validates_status_field()
    {
        $this->actingAs($this->user);

        $eventData = [
            'title' => 'Test Event',
            'duration' => 60,
            'booking_per_slot' => 1,
            'minimum_notice' => 0,
            'description' => 'Test description',
            'status' => 'invalid_status' // Invalid status
        ];

        $response = $this->postJson(route('calendar-events.store'), $eventData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['status']);
    }

    /** @test */
    public function it_includes_status_in_events_list()
    {
        $this->actingAs($this->user);

        // Create events with different statuses
        CalendarEvent::create([
            'title' => 'Active Event',
            'duration' => 60,
            'booking_per_slot' => 1,
            'minimum_notice' => 0,
            'status' => 'active',
            'created_by' => $this->user->id,
            'start_date' => now(),
            'end_date' => now()->addYear()
        ]);

        CalendarEvent::create([
            'title' => 'Inactive Event',
            'duration' => 60,
            'booking_per_slot' => 1,
            'minimum_notice' => 0,
            'status' => 'inactive',
            'created_by' => $this->user->id,
            'start_date' => now(),
            'end_date' => now()->addYear()
        ]);

        $response = $this->getJson(route('calendar-events.index'));

        $response->assertStatus(200)
                ->assertJson(['success' => true]);

        $events = $response->json('data');
        
        $this->assertCount(2, $events);
        
        $activeEvent = collect($events)->firstWhere('title', 'Active Event');
        $inactiveEvent = collect($events)->firstWhere('title', 'Inactive Event');
        
        $this->assertEquals('active', $activeEvent['status']);
        $this->assertEquals('inactive', $inactiveEvent['status']);
    }
}
