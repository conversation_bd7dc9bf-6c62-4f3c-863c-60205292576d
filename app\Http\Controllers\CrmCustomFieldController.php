<?php
namespace App\Http\Controllers;

use App\Models\CustomField;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
class CrmCustomFieldController extends Controller
{
    public function index()
    {
        $custom_fields = CustomField::where('module', 'Leads')->where('status', true)->get(); 
    
        return view('crm-custom-fields.index', compact('custom_fields'));
    }
    
    public function create()
    {
        $types = [
            'text' => 'Text',
            'textarea' => 'Text Area',
            'number' => 'Number',
            'email' => 'Email',
            'date' => 'Date Picker',
            'datetime' => 'DateTime Picker',
            'select' => 'Dropdown menu',
            'radio' => 'Radio button group',
            'checkbox' => 'Single or multiple checkbox options',
            'multiselect' => 'Dropdown with multiple selection',
            'link' => 'Link',
            'color' => 'Color Picker',
            'file' => 'Single file upload',
            'file_multiple' => 'Multiple file upload',
        ];
        
        $modules = ['Leads' => 'Leads']; 
        return view('crm-custom-fields.create', compact('types', 'modules'));
    }

    public function store(Request $request)
{
    $request->validate([
        'name' => 'required|string|max:191',
        'type' => 'required|string',
        'module' => 'required|string',
    ]);

    $base_key = '{{'.Str::slug($request->module, '_').'.'.Str::slug($request->name, '_').'}}';
    $unique_key = $base_key;
    $counter = 1;

    while (CustomField::where('unique_key', $unique_key)->exists()) {
        $unique_key = $base_key . '_' . $counter++;
    }

    // Get and encode options if checkbox, radio, select, or multiselect
    $options = null;
    if (in_array($request->type, ['checkbox', 'radio', 'select', 'multiselect']) && $request->has('options')) {
        $options = array_values(array_filter($request->options)); // clean empty values
    }

    CustomField::create([
        'name' => $request->name,
        'type' => $request->type,
        'module' => $request->module,
        'unique_key' => $unique_key,
        'created_by' => \Auth::user()->creatorId(), // Add this missing field!
        'options' => $options, // Remove json_encode, let the model handle it
    ]);

    return redirect()->back()->with('success', 'Custom field created successfully.');
}

    
    public function edit(CustomField $crmCustomField)
    {
        $types = [
            'text' => 'Text',
            'textarea' => 'Text Area',
            'number' => 'Number',
            'email' => 'Email',
            'date' => 'Date Picker',
            'datetime' => 'DateTime Picker',
            'select' => 'Dropdown menu',
            'radio' => 'Radio button group',
            'checkbox' => 'Single or multiple checkbox options',
            'multiselect' => 'Dropdown with multiple selection',
            'link' => 'Link',
            'color' => 'Color Picker',
            'file' => 'Single file upload',
            'file_multiple' => 'Multiple file upload',
        ];
        
        $modules = ['Leads' => 'Leads'];
        return view('crm-custom-fields.edit', compact('crmCustomField', 'types', 'modules'));
    }

    public function update(Request $request, CustomField $crmCustomField)
{
    $request->validate([
        'name' => 'required',
        'type' => 'required',
        'module' => 'required',
        'options' => 'nullable|string',
    ]);

    $data = $request->only('name', 'type', 'module');

    if (in_array($request->type, ['radio', 'checkbox', 'select', 'multiselect']) && $request->has('options')) {
        $options = array_values(array_filter($request->options)); // clean empty values
        $data['options'] = $options;
    } else {
        $data['options'] = null;
    }

    $crmCustomField->update($data);

    return redirect()->route('crmCustomField.index')->with('success', 'Custom field updated successfully.');
}

    public function destroy(CustomField $crmCustomField)
    {
        $crmCustomField->delete();
        return redirect()->route('crmCustomField.index')->with('success', 'Custom field deleted successfully.');
    }
}
