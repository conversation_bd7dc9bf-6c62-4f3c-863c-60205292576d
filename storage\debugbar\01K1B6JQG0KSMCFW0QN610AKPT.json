{"__meta": {"id": "01K1B6JQG0KSMCFW0QN610AKPT", "datetime": "2025-07-29 13:40:02", "utime": **********.688843, "method": "GET", "uri": "/dashboard", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"count": 4, "start": **********.127151, "end": **********.688865, "duration": 0.5617139339447021, "duration_str": "562ms", "measures": [{"label": "Booting", "start": **********.127151, "relative_start": 0, "end": **********.526208, "relative_end": **********.526208, "duration": 0.*****************, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.526216, "relative_start": 0.****************, "end": **********.688867, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.532984, "relative_start": 0.*****************, "end": **********.543341, "relative_end": **********.543341, "duration": 0.010356903076171875, "duration_str": "10.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.614099, "relative_start": 0.*****************, "end": **********.686642, "relative_end": **********.686642, "duration": 0.*****************, "duration_str": "72.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "1x dashboard.system_admin", "param_count": null, "params": [], "start": **********.615753, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.phpdashboard.system_admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fdashboard%2Fsystem_admin.blade.php&line=1", "ajax": false, "filename": "system_admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "dashboard.system_admin"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.636344, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.641806, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.67, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.682527, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.685424, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.685941, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET dashboard", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\DashboardController@clientView<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=605\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "client.dashboard.view", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=605\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/DashboardController.php:605-745</a>"}, "queries": {"count": 34, "nb_statements": 34, "nb_visible_statements": 34, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0256, "accumulated_duration_str": "25.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5615618, "duration": 0.010060000000000001, "duration_str": "10.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "omx_sass_db", "explain": null, "start_percent": 0, "width_percent": 39.297}, {"sql": "select * from `settings` where `created_by` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 301}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.579475, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "omx_sass_db", "explain": null, "start_percent": 39.297, "width_percent": 2.109}, {"sql": "select * from `system_admin_companies` where `system_admin_companies`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 527}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.583173, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:527", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 527}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=527", "ajax": false, "filename": "DashboardController.php", "line": "527"}, "connection": "omx_sass_db", "explain": null, "start_percent": 41.406, "width_percent": 1.719}, {"sql": "select count(*) as aggregate from `users` where `system_admin_company_id` = 1 and `type` in ('system admin', 'super admin')", "type": "query", "params": [], "bindings": [1, "system admin", "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 533}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.585105, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:533", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 533}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=533", "ajax": false, "filename": "DashboardController.php", "line": "533"}, "connection": "omx_sass_db", "explain": null, "start_percent": 43.125, "width_percent": 1.953}, {"sql": "select count(*) as aggregate from `users` where `system_admin_company_id` = 1 and `type` = 'super admin'", "type": "query", "params": [], "bindings": [1, "super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 536}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.587461, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:536", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 536}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=536", "ajax": false, "filename": "DashboardController.php", "line": "536"}, "connection": "omx_sass_db", "explain": null, "start_percent": 45.078, "width_percent": 2.227}, {"sql": "select count(*) as aggregate from `users` where `system_admin_company_id` = 1 and `type` = 'system admin'", "type": "query", "params": [], "bindings": [1, "system admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 539}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.589805, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:539", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 539}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=539", "ajax": false, "filename": "DashboardController.php", "line": "539"}, "connection": "omx_sass_db", "explain": null, "start_percent": 47.305, "width_percent": 1.406}, {"sql": "select count(*) as aggregate from `users` where `type` = 'company'", "type": "query", "params": [], "bindings": ["company"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 547}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.591788, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:547", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 547}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=547", "ajax": false, "filename": "DashboardController.php", "line": "547"}, "connection": "omx_sass_db", "explain": null, "start_percent": 48.711, "width_percent": 1.406}, {"sql": "select count(*) as aggregate from `users` where `type` = 'company' and `created_by` = 7", "type": "query", "params": [], "bindings": ["company", 7], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 549}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.593729, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:549", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 549}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=549", "ajax": false, "filename": "DashboardController.php", "line": "549"}, "connection": "omx_sass_db", "explain": null, "start_percent": 50.117, "width_percent": 1.328}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'omx_sass_db' and table_name = 'system_admin_modules' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 552}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.59557, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:552", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 552}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=552", "ajax": false, "filename": "DashboardController.php", "line": "552"}, "connection": "omx_sass_db", "explain": null, "start_percent": 51.445, "width_percent": 1.445}, {"sql": "select count(*) as aggregate from `system_admin_modules`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 553}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5979412, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:553", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 553}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=553", "ajax": false, "filename": "DashboardController.php", "line": "553"}, "connection": "omx_sass_db", "explain": null, "start_percent": 52.891, "width_percent": 1.133}, {"sql": "select count(*) as aggregate from `system_admin_modules` where `is_active` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 554}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5996351, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:554", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 554}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=554", "ajax": false, "filename": "DashboardController.php", "line": "554"}, "connection": "omx_sass_db", "explain": null, "start_percent": 54.023, "width_percent": 1.133}, {"sql": "select `name`, `is_active` from `system_admin_modules` order by `sort_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 559}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.60147, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:559", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=559", "ajax": false, "filename": "DashboardController.php", "line": "559"}, "connection": "omx_sass_db", "explain": null, "start_percent": 55.156, "width_percent": 1.25}, {"sql": "select * from `users` where `type` = 'super admin' order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 570}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.604214, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:570", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 570}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=570", "ajax": false, "filename": "DashboardController.php", "line": "570"}, "connection": "omx_sass_db", "explain": null, "start_percent": 56.406, "width_percent": 3.281}, {"sql": "select * from `users` where `type` = 'white_label' and `is_active` = 1", "type": "query", "params": [], "bindings": ["white_label", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 573}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 631}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.607116, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:573", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Http\\Controllers\\DashboardController.php", "line": 573}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=573", "ajax": false, "filename": "DashboardController.php", "line": "573"}, "connection": "omx_sass_db", "explain": null, "start_percent": 59.688, "width_percent": 2.305}, {"sql": "select * from `users` where `type` = 'sub_account'", "type": "query", "params": [], "bindings": ["sub_account"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.6188228, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "dashboard.system_admin:117", "source": {"index": 15, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 117}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fdashboard%2Fsystem_admin.blade.php&line=117", "ajax": false, "filename": "system_admin.blade.php", "line": "117"}, "connection": "omx_sass_db", "explain": null, "start_percent": 61.992, "width_percent": 2.305}, {"sql": "select * from `product_service_categories` where `created_by` = 0 and `type` = 'expense' and `name` = 'Marketing' limit 1", "type": "query", "params": [], "bindings": [0, "expense", "Marketing"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 188}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.624605, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "dashboard.system_admin:188", "source": {"index": 16, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fdashboard%2Fsystem_admin.blade.php&line=188", "ajax": false, "filename": "system_admin.blade.php", "line": "188"}, "connection": "omx_sass_db", "explain": null, "start_percent": 64.297, "width_percent": 2.109}, {"sql": "select * from `product_service_categories` where `created_by` = 0 and `type` = 'expense' and `name` = 'Overheads' limit 1", "type": "query", "params": [], "bindings": [0, "expense", "Overheads"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 188}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.626833, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "dashboard.system_admin:188", "source": {"index": 16, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fdashboard%2Fsystem_admin.blade.php&line=188", "ajax": false, "filename": "system_admin.blade.php", "line": "188"}, "connection": "omx_sass_db", "explain": null, "start_percent": 66.406, "width_percent": 1.523}, {"sql": "select * from `product_service_categories` where `created_by` = 0 and `type` = 'expense' and `name` = 'Affiliates' limit 1", "type": "query", "params": [], "bindings": [0, "expense", "Affiliates"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 188}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.628802, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "dashboard.system_admin:188", "source": {"index": 16, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fdashboard%2Fsystem_admin.blade.php&line=188", "ajax": false, "filename": "system_admin.blade.php", "line": "188"}, "connection": "omx_sass_db", "explain": null, "start_percent": 67.93, "width_percent": 1.406}, {"sql": "select * from `product_service_categories` where `created_by` = 0 and `type` = 'expense' and `name` = 'Others' limit 1", "type": "query", "params": [], "bindings": [0, "expense", "Others"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 188}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.6306891, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "dashboard.system_admin:188", "source": {"index": 16, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fdashboard%2Fsystem_admin.blade.php&line=188", "ajax": false, "filename": "system_admin.blade.php", "line": "188"}, "connection": "omx_sass_db", "explain": null, "start_percent": 69.336, "width_percent": 1.289}, {"sql": "select * from `product_service_categories` where `created_by` = 0 and `type` = 'income'", "type": "query", "params": [], "bindings": [0, "income"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 196}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.6325011, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "dashboard.system_admin:196", "source": {"index": 15, "namespace": "view", "name": "dashboard.system_admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/dashboard/system_admin.blade.php", "line": 196}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fdashboard%2Fsystem_admin.blade.php&line=196", "ajax": false, "filename": "system_admin.blade.php", "line": "196"}, "connection": "omx_sass_db", "explain": null, "start_percent": 70.625, "width_percent": 1.289}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6372669, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 71.914, "width_percent": 2.969}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.639672, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 74.883, "width_percent": 1.719}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.644366, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 76.602, "width_percent": 2.07}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.646759, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": {"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\EmailTemplate.php", "line": 27}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "omx_sass_db", "explain": null, "start_percent": 78.672, "width_percent": 1.563}, {"sql": "select * from `users` where `id` = 0 limit 1", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 1401}, {"index": 17, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 34}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.648814, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "User.php:1401", "source": {"index": 16, "namespace": null, "name": "app/Models/User.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\User.php", "line": 1401}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1401", "ajax": false, "filename": "User.php", "line": "1401"}, "connection": "omx_sass_db", "explain": null, "start_percent": 80.234, "width_percent": 1.875}, {"sql": "select * from `plans` where `plans`.`id` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Plan.php", "line": 67}, {"index": 21, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/menu.blade.php", "line": 34}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.651022, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Plan.php:67", "source": {"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Plan.php", "line": 67}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FPlan.php&line=67", "ajax": false, "filename": "Plan.php", "line": "67"}, "connection": "omx_sass_db", "explain": null, "start_percent": 82.109, "width_percent": 1.875}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (7) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.6603699, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "omx_sass_db", "explain": null, "start_percent": 83.984, "width_percent": 2.148}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (7) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 242}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.6624422, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:242", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=242", "ajax": false, "filename": "HasRoles.php", "line": "242"}, "connection": "omx_sass_db", "explain": null, "start_percent": 86.133, "width_percent": 1.758}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.67058, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 87.891, "width_percent": 2.773}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'omx_sass_db' and table_name = 'languages' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 6}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.673074, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:527", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 527}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=527", "ajax": false, "filename": "Utility.php", "line": "527"}, "connection": "omx_sass_db", "explain": null, "start_percent": 90.664, "width_percent": 2.422}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 6}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.6756198, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:533", "source": {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 533}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=533", "ajax": false, "filename": "Utility.php", "line": "533"}, "connection": "omx_sass_db", "explain": null, "start_percent": 93.086, "width_percent": 1.641}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4320}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 16}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.677813, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 94.727, "width_percent": 1.836}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 7 and `seen` = 0", "type": "query", "params": [], "bindings": [7, 0], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 20}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.6800418, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:20", "source": {"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/header.blade.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=20", "ajax": false, "filename": "header.blade.php", "line": "20"}, "connection": "omx_sass_db", "explain": null, "start_percent": 96.563, "width_percent": 1.641}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 5748}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6829832, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4359", "source": {"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\Users\\<USER>\\Desktop\\new_laravel_project\\omx-new-saas\\app\\Models\\Utility.php", "line": 4359}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUtility.php&line=4359", "ajax": false, "filename": "Utility.php", "line": "4359"}, "connection": "omx_sass_db", "explain": null, "start_percent": 98.203, "width_percent": 1.797}]}, "models": {"data": {"App\\Models\\SystemAdminModule": {"retrieved": 21, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FSystemAdminModule.php&line=1", "ajax": false, "filename": "SystemAdminModule.php", "line": "?"}}, "App\\Models\\User": {"retrieved": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\SystemAdminCompany": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FSystemAdminCompany.php&line=1", "ajax": false, "filename": "SystemAdminCompany.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"retrieved": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 30, "key_map": {"retrieved": "Retrieved", "created": "Created", "updated": "Updated", "deleted": "Deleted"}, "is_counter": true, "badges": {"retrieved": 30}}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 6, "messages": [{"message": "[\n  ability => show hrm dashboard,\n  target => null,\n  result => null,\n  user => 7,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-670051633 data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-670051633\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.666352, "xdebug_link": null}, {"message": "[\n  ability => show project dashboard,\n  target => null,\n  result => null,\n  user => 7,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show project dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show project dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.666917, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  target => null,\n  result => null,\n  user => 7,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.667152, "xdebug_link": null}, {"message": "[\n  ability => show crm dashboard,\n  target => null,\n  result => null,\n  user => 7,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show crm dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show crm dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.667384, "xdebug_link": null}, {"message": "[\n  ability => show pos dashboard,\n  target => null,\n  result => null,\n  user => 7,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-104825227 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-104825227\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.667604, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  target => null,\n  result => null,\n  user => 7,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1108592973 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1108592973\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.667952, "xdebug_link": null}]}, "session": {"_token": "S0GC30PPz1LGMCuYEzpBQyRIthmbPuLlyeICi6cg", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "7"}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/dashboard", "action_name": "client.dashboard.view", "controller_action": "App\\Http\\Controllers\\DashboardController@clientView", "uri": "GET dashboard", "controller": "App\\Http\\Controllers\\DashboardController@clientView<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=605\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fbarma%2FDesktop%2Fnew_laravel_project%2Fomx-new-saas%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=605\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/DashboardController.php:605-745</a>", "middleware": "web, verified, auth, XSS", "duration": "566ms", "peak_memory": "58MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-241325985 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-241325985\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-49622697 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-49622697\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1243514591 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/contact-groups</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3260 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjZVREVBbC9XSnIwcnNtSE5nWGdkbWc9PSIsInZhbHVlIjoiSUZqRHlsTHl1NWFFNVYzbTdnRFhIdW5DUjY0RVFjV01mOXFNSnU5aFpBcnpsV3BWT21OZUwyM0pVMWxNT2VYZUlEa1A2WHU4cDYveHF6NVF3N1ZnV3lNY2pLM1BLUkJOMlZJZXJ0SUxnTjJJSC9BYmNKbHp1V1FlWGZNL3IweVM4Mi9jcUw4U3ZBYnZoY2JaRTkzbGo2L2pSUlVxMGk3b0wxUWZ2d2JkSUlwTDZnbUFsc3BRSGh2ZVRnZFBiV1o5aTlZcXlncElWVDA2VXdhei8wVEE2bXQzWkpUbSthTDI4eDNOaGtja0ZMVT0iLCJtYWMiOiJmYzk1YjdhMmFhYmZjYTM1ZjAxZWM4NTA5NWEyNWRmYjE1OWUxYjlkMTQ1NzUzMTY5NDM2YTk0Y2EyMDc5ZmI3IiwidGFnIjoiIn0%3D; omx_ai_suite_session=eyJpdiI6InQwbGxlWE9SVkptWHd6S3M3Mk9ITHc9PSIsInZhbHVlIjoiYzh4bmk4YmxhMjVnVXczK2tEcXF2aXNFZ0hjck0vK0F0UXRVaGltdHpZVi9XZkhHbmFDOGFoM2llZ2VzV3M1NE50Q29qb1VVTm1NZ0RERndSUVFTdytyUDc3cTFxS0R4Si8vdWF1VlJsOVpJZTdPbFZhM1dOTnFEWFJ0YVJRWGFSdjNvT0FqR0FVV29PaGxYVHhwZUhTNklSU1VHQkZpMDc0c1ZuOEhpb3NzVkxobTIxZXBKUWxuOTRCYUQwR3krbG5OODE3emx3TXRxSTlMbmlLT2E5NitGNWJKZFhycXVxdHJwQ3BHdEJYWENTbkorS05HaFFRd2JYRU5NYUV6MXc0TWhSNXNDSGNtNWpPME5iWWJwUVpFQ1BMZ3NNUHUxaStVaVBja2xXM0c1UEUvTjFTM3doMVE0NC9mbG5SQ2s4OWlCMldZandacFZvL0t6RTMvcDNlckpzdktuV0hIQkNKNno5VnI4OGF6VzkxdzlMNDBCZEtJb1JXcHZzUEgzUEZldDA1dmY1b0h2NXhHbDlsODRkMWJsTkFjZzU1NkJ5b0VPcnQ4T25vQ1NidlNVYVAyYnRRczcvZ016Q3BUSzNtRExvdFJGTktoU1VDK21LSnU1bFVpY1dzdUFvbGx2MTllbDlIcU8xVDFLSjBNNk44MHc3eE8xWXBSQ3QyeGoiLCJtYWMiOiIxNDIyMDViMzgzMTU2Njc5M2FlYzJjOTJiM2M3YTgxYjE3YTdkYzY0MWE1NDIwNjkyYzc5ZDE1ZDYxNDc4NTU0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVxSVVBNzlTc0RHZlpzNytsOHhJWnc9PSIsInZhbHVlIjoiVjZUTXdMSG9JYmJ6NWxIK1pISU9kQ095MmNFd1dBbVFtbTZwTnRabU5wU1U5UVNVcTNZbVN1andvZFg0ZEoyL3ZZTG8xdVdNNDJGMHFFblF3T2V1R0loUm1aL0hNTnhiTDhieDd4V1AyTi9YamtqRVNyQU54c3dHQzlWdUlDNWlPSlZ3ck1PZDlDSEhETXVyNjRjcVcwUWhrcmR1a0tRaEhDTUdmeXpOMU1pcnlja2MxZi96dXJuaVB6cndHRFdSOFA0dkRPem5namFNTi8yZ3QzeEFvSksxNWxxMkJIM2hmYkduTHREQzhTSUZXd1NrdVJyd2VnZWtXNlRINm1UZlJCMkhDZkpWejhJenJsUzZrVEM2MHR3aVJzUC9DRkd3V3ZvaVlmUGVadStUckZFWmxSbFNZNnBza0lRcXlCaXZoZ0ovSUM4Q0lKcUdBZTNBM1NlYzhobW9pNi9zSHFjNHpFclUwOEpoRjBSa3RTK0x1UGRBOGVkeUZUSnc2T2cxQU9TaVMwcUpCL1ltQWV2ZklGSGh6ZlV2VDZOYWJMY0c2cjEzaDBsRTZOZytmNERpMTYzNU1USmlKS3VxNWZvM2huNVdTcGYzTm8yZlFYTWVHQ2dpVjVOZFpHVTdoYm1nOXdOU1RsdUFoQXBLNGloSGdDZjV6cUNDN0t2bWVIVlUiLCJtYWMiOiI4ODM5Mzc3OTk4MTI1OTJjMGVmZWNhNmNkZmFmNmRhMTllMmJjMzZhNWY2NzFlMGNjZmU1MzJmMDU2MTE2ZDA2IiwidGFnIjoiIn0%3D; krishna_session=eyJpdiI6IlZUWlBiZXd6bkgzUGxEUzNQeGI3bWc9PSIsInZhbHVlIjoiSEtGOHV1cW5malJobTMwM1p6TnB4RnhvZ1IwZDR3YXh2Qk04Y3h5VU1ZdWJIbTl4eEo2Sy92ZEJiN3JpYlhaSG94elFEeEw2dmZ5b1k5Qm90aEtXSHc1a1h4cytOVk5tK3NNRHpHbGVDMGUySG9TcG9qZVp5NGdhR1hEdEM2am9iMUxYemFlcHhQaVkwb1ZLNHNwSmZXQWxpQlJnWGY3MHVsSitkZ0srZDBxaFZLT09OcTJkaVQ1d0NEK3VZSUJXS2djSU5LTm44VWx1UHJlQjdCcGJzcEp4eXFyZ0JzWFhQSmtNbDZXdUNQeDA0T3EwcHVjd0h0UUFFaGhRUjdwdEVvdXNvL3VqQU44ZEREdjlzWWRIYVZBN0ZEV3BmQTdxRWgvcmFiVHc0T0J1RjRBYVdtNzB2YW4zVkRmMEVvWC9wUUxWQUxvQjgvaFhrelhGdERuVFJFUU9ZQ3dGMlB3dy80VStROURUaFZZTDhOUE1EaytndXR3WGNFZEg1bE9iYmdkZEFLcFNleTg3eXRtMWx4N1ZqWHJWNGorUmFPZGJRaGliWWhnSCszbWx0TktFeTllZjg4eVEzajNKOHlsaG1kc08zKzJDRFp5YXE4cWVnaUs2Y2xJRytIUmt0VUVNUXhkTHFraGtVYjBFQ0IwanlEOCtGWWdUaE9RdkpJNE0iLCJtYWMiOiJjY2FkNmM3ODdmZTBiYzI2ODE0NmZkNTU4YmU4YzkyNjlkNjAxM2VlMzUwODQ4YmJmMTljYmRiZTgwMDZiMjQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1243514591\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-638922243 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>omx_ai_suite_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kWrcfTjCL8MxHY4tTylESI5sjjMcRCglPG5z0ltT</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">S0GC30PPz1LGMCuYEzpBQyRIthmbPuLlyeICi6cg</span>\"\n  \"<span class=sf-dump-key>krishna_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">scvU0ACtyQUqPRXYaB2G0jWgUbcSuWfpphoDjrnk</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-638922243\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-326698968 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 29 Jul 2025 13:40:02 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-326698968\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1660084459 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">S0GC30PPz1LGMCuYEzpBQyRIthmbPuLlyeICi6cg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://127.0.0.1:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>7</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1660084459\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/dashboard", "action_name": "client.dashboard.view", "controller_action": "App\\Http\\Controllers\\DashboardController@clientView"}, "badge": null}}