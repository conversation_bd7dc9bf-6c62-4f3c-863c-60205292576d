<!-- Transactions Tab Content -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ __('Transaction Management') }}</h4>
            <div class="d-flex gap-2">
                <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                    <i class="ti ti-plus me-1"></i>{{ __('Add Transaction') }}
                </button>
                <a href="{{ route('transaction.index') }}" class="btn btn-primary btn-sm">
                    <i class="ti ti-eye me-1"></i>{{ __('View All') }}
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Stats -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 bg-success text-white">
            <div class="card-body text-center">
                <i class="ti ti-trending-up" style="font-size: 2rem;"></i>
                <h4 class="mt-2 mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                <small>{{ __('Total Income') }}</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 bg-danger text-white">
            <div class="card-body text-center">
                <i class="ti ti-trending-down" style="font-size: 2rem;"></i>
                <h4 class="mt-2 mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                <small>{{ __('Total Expenses') }}</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 bg-primary text-white">
            <div class="card-body text-center">
                <i class="ti ti-arrows-exchange" style="font-size: 2rem;"></i>
                <h4 class="mt-2 mb-1">0</h4>
                <small>{{ __('Total Transactions') }}</small>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card border-0 bg-info text-white">
            <div class="card-body text-center">
                <i class="ti ti-wallet" style="font-size: 2rem;"></i>
                <h4 class="mt-2 mb-1">{{ \Auth::user()->priceFormat(0) }}</h4>
                <small>{{ __('Net Balance') }}</small>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('Recent Transactions') }}</h5>
                <div class="d-flex gap-2">
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="ti ti-filter me-1"></i>{{ __('Filter') }}
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-filter="all">{{ __('All Transactions') }}</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="income">{{ __('Income Only') }}</a></li>
                            <li><a class="dropdown-item" href="#" data-filter="expense">{{ __('Expense Only') }}</a></li>
                        </ul>
                    </div>
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="ti ti-download me-1"></i>{{ __('Export') }}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{{ __('Date') }}</th>
                                <th>{{ __('Description') }}</th>
                                <th>{{ __('Category') }}</th>
                                <th>{{ __('Account') }}</th>
                                <th>{{ __('Amount') }}</th>
                                <th>{{ __('Type') }}</th>
                                <th>{{ __('Action') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Sample transactions -->
                            <tr>
                                <td>{{ now()->format('M d, Y') }}</td>
                                <td>{{ __('Sample Income Transaction') }}</td>
                                <td><span class="badge bg-light text-dark">{{ __('Sales') }}</span></td>
                                <td>{{ __('Main Account') }}</td>
                                <td class="text-success">+{{ \Auth::user()->priceFormat(1000) }}</td>
                                <td><span class="badge bg-success">{{ __('Income') }}</span></td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-sm btn-outline-primary" title="{{ __('View') }}">
                                            <i class="ti ti-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" title="{{ __('Edit') }}">
                                            <i class="ti ti-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="{{ __('Delete') }}">
                                            <i class="ti ti-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>{{ now()->subDay()->format('M d, Y') }}</td>
                                <td>{{ __('Sample Expense Transaction') }}</td>
                                <td><span class="badge bg-light text-dark">{{ __('Office Supplies') }}</span></td>
                                <td>{{ __('Main Account') }}</td>
                                <td class="text-danger">-{{ \Auth::user()->priceFormat(250) }}</td>
                                <td><span class="badge bg-danger">{{ __('Expense') }}</span></td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-sm btn-outline-primary" title="{{ __('View') }}">
                                            <i class="ti ti-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-secondary" title="{{ __('Edit') }}">
                                            <i class="ti ti-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" title="{{ __('Delete') }}">
                                            <i class="ti ti-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="ti ti-arrows-exchange" style="font-size: 3rem;"></i>
                                        <h5 class="mt-3">{{ __('No More Transactions') }}</h5>
                                        <p>{{ __('Add transactions to track your financial activities') }}</p>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                                            <i class="ti ti-plus me-1"></i>{{ __('Add Transaction') }}
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('Quick Actions') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#addIncomeModal">
                            <i class="ti ti-plus mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Add Income') }}</span>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-danger w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3" data-bs-toggle="modal" data-bs-target="#addExpenseModal">
                            <i class="ti ti-minus mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Add Expense') }}</span>
                        </button>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('bank-transfer.index') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-arrows-exchange mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Transfer') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('bank-account.index') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-building-bank mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Accounts') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <a href="{{ route('report.income.summary') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-chart-line mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Reports') }}</span>
                        </a>
                    </div>
                    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                        <button class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="ti ti-download mb-2" style="font-size: 1.5rem;"></i>
                            <span>{{ __('Export') }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Transaction Modal -->
<div class="modal fade" id="addTransactionModal" tabindex="-1" aria-labelledby="addTransactionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTransactionModalLabel">{{ __('Add Transaction') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="transactionType" class="form-label">{{ __('Transaction Type') }}</label>
                        <select class="form-select" id="transactionType">
                            <option value="income">{{ __('Income') }}</option>
                            <option value="expense">{{ __('Expense') }}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="transactionAmount" class="form-label">{{ __('Amount') }}</label>
                        <input type="number" class="form-control" id="transactionAmount" placeholder="0.00">
                    </div>
                    <div class="mb-3">
                        <label for="transactionDescription" class="form-label">{{ __('Description') }}</label>
                        <input type="text" class="form-control" id="transactionDescription" placeholder="{{ __('Transaction description') }}">
                    </div>
                    <div class="mb-3">
                        <label for="transactionCategory" class="form-label">{{ __('Category') }}</label>
                        <select class="form-select" id="transactionCategory">
                            <option value="">{{ __('Select Category') }}</option>
                            <option value="sales">{{ __('Sales') }}</option>
                            <option value="office">{{ __('Office Supplies') }}</option>
                            <option value="marketing">{{ __('Marketing') }}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="transactionDate" class="form-label">{{ __('Date') }}</label>
                        <input type="date" class="form-control" id="transactionDate" value="{{ date('Y-m-d') }}">
                    </div>
                    <p class="text-muted small">{{ __('Transaction management feature will be enhanced soon.') }}</p>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-success" disabled>{{ __('Add Transaction') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Income Modal -->
<div class="modal fade" id="addIncomeModal" tabindex="-1" aria-labelledby="addIncomeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addIncomeModalLabel">{{ __('Add Income') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="incomeAmount" class="form-label">{{ __('Amount') }}</label>
                        <input type="number" class="form-control" id="incomeAmount" placeholder="0.00">
                    </div>
                    <div class="mb-3">
                        <label for="incomeDescription" class="form-label">{{ __('Description') }}</label>
                        <input type="text" class="form-control" id="incomeDescription" placeholder="{{ __('Income description') }}">
                    </div>
                    <div class="mb-3">
                        <label for="incomeDate" class="form-label">{{ __('Date') }}</label>
                        <input type="date" class="form-control" id="incomeDate" value="{{ date('Y-m-d') }}">
                    </div>
                    <p class="text-muted small">{{ __('Quick income entry will be available soon.') }}</p>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-success" disabled>{{ __('Add Income') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Expense Modal -->
<div class="modal fade" id="addExpenseModal" tabindex="-1" aria-labelledby="addExpenseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addExpenseModalLabel">{{ __('Add Expense') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="expenseAmount" class="form-label">{{ __('Amount') }}</label>
                        <input type="number" class="form-control" id="expenseAmount" placeholder="0.00">
                    </div>
                    <div class="mb-3">
                        <label for="expenseDescription" class="form-label">{{ __('Description') }}</label>
                        <input type="text" class="form-control" id="expenseDescription" placeholder="{{ __('Expense description') }}">
                    </div>
                    <div class="mb-3">
                        <label for="expenseDate" class="form-label">{{ __('Date') }}</label>
                        <input type="date" class="form-control" id="expenseDate" value="{{ date('Y-m-d') }}">
                    </div>
                    <p class="text-muted small">{{ __('Quick expense entry will be available soon.') }}</p>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-danger" disabled>{{ __('Add Expense') }}</button>
            </div>
        </div>
    </div>
</div>
