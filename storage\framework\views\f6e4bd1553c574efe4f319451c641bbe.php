<?php
    $currentRoute = Request::route()->getName();
    $currentSegment = Request::segment(1);
?>

<nav class="crm-nav">
    <div class="crm-nav-container">
        <a href="<?php echo e(route('leads.index')); ?>" class="crm-nav-item <?php if((in_array($currentRoute, ['leads.list', 'leads.index', 'leads.show']) || $currentSegment == 'leads') && !request()->get('filter')): ?> active <?php endif; ?>">
            <i class="ti ti-users"></i>
            <span><?php echo e(__('CRM')); ?></span>
        </a>
        
        <a href="<?php echo e(route('form_builder.index')); ?>" class="crm-nav-item <?php if(in_array($currentRoute, ['form_builder.index', 'form_builder.show']) || $currentSegment == 'form_builder' || $currentSegment == 'form_response'): ?> active <?php endif; ?>">
            <i class="ti ti-forms"></i>
            <span><?php echo e(__('Form Builder')); ?></span>
        </a>
        
        <a href="<?php echo e(route('contacts.index')); ?>" class="crm-nav-item <?php if(in_array($currentRoute, ['contacts.list', 'contacts.index', 'contacts.show']) || $currentSegment == 'contacts'): ?> active <?php endif; ?>">
            <i class="ti ti-address-book"></i>
            <span><?php echo e(__('Contacts')); ?></span>
        </a>
        
        <a href="<?php echo e(route('contact-groups.index')); ?>" class="crm-nav-item <?php if(in_array($currentRoute, ['contact-groups.index', 'contact-groups.show']) || $currentSegment == 'contact-groups'): ?> active <?php endif; ?>">
            <i class="ti ti-users-group"></i>
            <span><?php echo e(__('Contact Group')); ?></span>
        </a>
        
        <a href="<?php echo e(route('pipeline.index')); ?>" class="crm-nav-item <?php if(in_array($currentRoute, ['pipeline.index', 'pipelines.index', 'pipelines.create', 'pipelines.edit']) || $currentSegment == 'pipelines' || $currentSegment == 'pipeline'): ?> active <?php endif; ?>">
            <i class="ti ti-git-branch"></i>
            <span><?php echo e(__('Pipelines')); ?></span>
        </a>
    </div>
</nav>

<style>
.crm-nav {
    background: #fff;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 1.5rem;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.crm-nav-container {
    display: flex;
    align-items: center;
    gap: 0;
    max-width: 100%;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.crm-nav-container::-webkit-scrollbar {
    display: none;
}

.crm-nav-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    color: #6b7280;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    white-space: nowrap;
    position: relative;
}

.crm-nav-item:hover {
    color: #374151;
    background: #f9fafb;
}

.crm-nav-item.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: #eff6ff;
}

.crm-nav-item i {
    font-size: 1rem;
}

.crm-nav-item span {
    font-size: 0.875rem;
}

@media (max-width: 768px) {
    .crm-nav-item {
        padding: 0.625rem 1rem;
        font-size: 0.8rem;
    }
    
    .crm-nav-item i {
        font-size: 0.9rem;
    }
    
    .crm-nav-item span {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .crm-nav-item {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }
    
    .crm-nav-item i {
        font-size: 0.85rem;
    }
    
    .crm-nav-item span {
        font-size: 0.75rem;
    }
}
</style> <?php /**PATH C:\Users\<USER>\Desktop\new_laravel_project\omx-new-saas\resources\views/partials/admin/crm-top-nav.blade.php ENDPATH**/ ?>