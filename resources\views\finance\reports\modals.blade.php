<!-- Overdue Report Modal -->
<div class="modal fade" id="overdueModal" tabindex="-1" aria-labelledby="overdueModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="overdueModalLabel">{{ __('Overdue Report') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">{{ __('Overdue report feature will be available soon. This will show all overdue invoices and payments.') }}</p>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{{ __('Invoice') }}</th>
                                <th>{{ __('Customer') }}</th>
                                <th>{{ __('Amount') }}</th>
                                <th>{{ __('Due Date') }}</th>
                                <th>{{ __('Days Overdue') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="5" class="text-center text-muted">{{ __('No overdue invoices found') }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- GST Report Modal -->
<div class="modal fade" id="gstModal" tabindex="-1" aria-labelledby="gstModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="gstModalLabel">{{ __('GST Report') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">{{ __('GST report feature will be available soon. This will provide detailed tax calculations and GST summaries.') }}</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                <h5 class="text-success">{{ \Auth::user()->priceFormat(0) }}</h5>
                                <small class="text-muted">{{ __('GST Collected') }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card border-0 bg-light">
                            <div class="card-body text-center">
                                <h5 class="text-danger">{{ \Auth::user()->priceFormat(0) }}</h5>
                                <small class="text-muted">{{ __('GST Paid') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Customer Report Modal -->
<div class="modal fade" id="customerModal" tabindex="-1" aria-labelledby="customerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customerModalLabel">{{ __('Customer Report') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">{{ __('Customer report feature will be available soon. This will provide detailed customer analytics and insights.') }}</p>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{{ __('Customer') }}</th>
                                <th>{{ __('Total Sales') }}</th>
                                <th>{{ __('Outstanding') }}</th>
                                <th>{{ __('Last Purchase') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="4" class="text-center text-muted">{{ __('No customer data available') }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Custom Report Modal -->
<div class="modal fade" id="customReportModal" tabindex="-1" aria-labelledby="customReportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="customReportModalLabel">{{ __('Create Custom Report') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="reportName" class="form-label">{{ __('Report Name') }}</label>
                        <input type="text" class="form-control" id="reportName" placeholder="{{ __('Enter report name') }}">
                    </div>
                    <div class="mb-3">
                        <label for="reportType" class="form-label">{{ __('Report Type') }}</label>
                        <select class="form-select" id="reportType">
                            <option value="">{{ __('Select report type') }}</option>
                            <option value="income">{{ __('Income Report') }}</option>
                            <option value="expense">{{ __('Expense Report') }}</option>
                            <option value="customer">{{ __('Customer Report') }}</option>
                            <option value="product">{{ __('Product Report') }}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="dateRange" class="form-label">{{ __('Date Range') }}</label>
                        <select class="form-select" id="dateRange">
                            <option value="today">{{ __('Today') }}</option>
                            <option value="week">{{ __('This Week') }}</option>
                            <option value="month">{{ __('This Month') }}</option>
                            <option value="quarter">{{ __('This Quarter') }}</option>
                            <option value="year">{{ __('This Year') }}</option>
                            <option value="custom">{{ __('Custom Range') }}</option>
                        </select>
                    </div>
                    <p class="text-muted small">{{ __('Custom report builder will be available soon.') }}</p>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-primary" disabled>{{ __('Create Report') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exportModalLabel">{{ __('Export Reports') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">{{ __('Select Reports to Export') }}</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="exportIncome">
                        <label class="form-check-label" for="exportIncome">{{ __('Income Report') }}</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="exportExpense">
                        <label class="form-check-label" for="exportExpense">{{ __('Expense Report') }}</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="exportPL">
                        <label class="form-check-label" for="exportPL">{{ __('Profit & Loss') }}</label>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="exportFormat" class="form-label">{{ __('Export Format') }}</label>
                    <select class="form-select" id="exportFormat">
                        <option value="pdf">{{ __('PDF') }}</option>
                        <option value="excel">{{ __('Excel') }}</option>
                        <option value="csv">{{ __('CSV') }}</option>
                    </select>
                </div>
                <p class="text-muted small">{{ __('Export functionality will be available soon.') }}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-success" disabled>{{ __('Export') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Schedule Modal -->
<div class="modal fade" id="scheduleModal" tabindex="-1" aria-labelledby="scheduleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="scheduleModalLabel">{{ __('Schedule Reports') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="scheduleFrequency" class="form-label">{{ __('Frequency') }}</label>
                        <select class="form-select" id="scheduleFrequency">
                            <option value="daily">{{ __('Daily') }}</option>
                            <option value="weekly">{{ __('Weekly') }}</option>
                            <option value="monthly">{{ __('Monthly') }}</option>
                            <option value="quarterly">{{ __('Quarterly') }}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="scheduleEmail" class="form-label">{{ __('Email Recipients') }}</label>
                        <input type="email" class="form-control" id="scheduleEmail" placeholder="{{ __('Enter email addresses') }}">
                    </div>
                    <p class="text-muted small">{{ __('Scheduled reports feature will be available soon.') }}</p>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-warning" disabled>{{ __('Schedule') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Share Modal -->
<div class="modal fade" id="shareModal" tabindex="-1" aria-labelledby="shareModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="shareModalLabel">{{ __('Share Reports') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="shareEmail" class="form-label">{{ __('Share with') }}</label>
                    <input type="email" class="form-control" id="shareEmail" placeholder="{{ __('Enter email address') }}">
                </div>
                <div class="mb-3">
                    <label for="shareMessage" class="form-label">{{ __('Message') }}</label>
                    <textarea class="form-control" id="shareMessage" rows="3" placeholder="{{ __('Optional message') }}"></textarea>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="shareLink">
                        <label class="form-check-label" for="shareLink">{{ __('Generate shareable link') }}</label>
                    </div>
                </div>
                <p class="text-muted small">{{ __('Report sharing feature will be available soon.') }}</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Close') }}</button>
                <button type="button" class="btn btn-primary" disabled>{{ __('Share') }}</button>
            </div>
        </div>
    </div>
</div>
